<?php

namespace removemetadata;

use Craft;
use removemetadata\services\MetadataCleanupService;
use yii\base\Module;

class RemoveMetadataModule extends Module
{
    public function init(): void
    {
        parent::init();

        // Register services
        $this->setComponents([
            'cleanup' => MetadataCleanupService::class,
        ]);

        if (Craft::$app->getRequest()->getIsConsoleRequest()) {
            $this->controllerNamespace = 'removemetadata\\controllers';
        }
    }

    public function getCleanupService(): MetadataCleanupService
    {
        /** @var MetadataCleanupService */
        return $this->get('cleanup');
    }
}