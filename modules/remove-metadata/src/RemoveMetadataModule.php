<?php

namespace removemetadata;

use Craft;
use craft\base\Element;
use craft\elements\Asset;
use craft\queue\Queue;
use removemetadata\jobs\MetadataCleanupJob;
use removemetadata\services\MetadataCleanupService;
use yii\base\Event;
use yii\base\Module;

class RemoveMetadataModule extends Module
{
    public function init(): void
    {
        parent::init();

        // Register services
        $this->setComponents([
            'cleanup' => MetadataCleanupService::class,
        ]);

        if (Craft::$app->getRequest()->getIsConsoleRequest()) {
            $this->controllerNamespace = 'removemetadata\\controllers';
        }

        // Set the controller path alias
        Craft::setAlias('@removemetadata/controllers', __DIR__ . '/controllers');

        // Register event handlers for automatic metadata removal
        $this->attachEventHandlers();
    }

    public function getCleanupService(): MetadataCleanupService
    {
        /** @var MetadataCleanupService */
        return $this->get('cleanup');
    }

    /**
     * Attach event handlers for automatic metadata removal
     */
    private function attachEventHandlers(): void
    {
        // Process assets after they are saved (new uploads or file replacements)
        Event::on(
            Asset::class,
            Element::EVENT_AFTER_SAVE,
            function (Event $event) {
                /** @var Asset $asset */
                $asset = $event->sender;

                // Only process if this is a new asset or if the file was replaced
                if ($asset->getScenario() === Asset::SCENARIO_CREATE ||
                    $asset->getScenario() === Asset::SCENARIO_FILEOPS) {

                    // Queue the metadata cleanup job to avoid blocking the upload
                    Craft::$app->getQueue()->push(new MetadataCleanupJob([
                        'assetId' => $asset->id,
                    ]));

                    Craft::info("Queued metadata cleanup for asset {$asset->id} ({$asset->getFilename()})", __METHOD__);
                }
            }
        );
    }
}