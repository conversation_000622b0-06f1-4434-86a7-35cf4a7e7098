<?php

namespace removemetadata;

use Craft;
use craft\base\Element;
use craft\elements\Asset;
use yii\base\Event;
use yii\base\Module;
use ZipArchive;

class RemoveMetadataModule extends Module
{
    public function init(): void
    {
        Craft::setAlias('@removemetadata', __DIR__);

        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'removemetadata\\console\\controllers';
        } else {
            $this->controllerNamespace = 'removemetadata\\controllers';
        }

        parent::init();

        // Defer event registration until Craft is fully initialized
        Craft::$app->onInit(function() {
            $this->attachEventHandlers();
        });
    }

    /**
     * Strip metadata from an asset
     */
    public function stripMetadata(Asset $asset): bool
    {
        $extension = strtolower(pathinfo($asset->getFilename(), PATHINFO_EXTENSION));

        // Only process Office documents - they commonly contain author metadata
        if (!in_array($extension, ['docx', 'xlsx', 'pptx'])) {
            return true; // Skip other files
        }

        $fs = $asset->getVolume()->getFs();
        if (!$fs->fileExists($asset->getPath())) {
            return false;
        }

        try {
            // Get file content
            $stream = $fs->getFileStream($asset->getPath());
            $content = stream_get_contents($stream);
            fclose($stream);

            // Process Office document
            $tempFile = tempnam(sys_get_temp_dir(), 'metadata_');
            file_put_contents($tempFile, $content);

            if ($this->processOfficeDocument($tempFile)) {
                Craft::$app->getAssets()->replaceAssetFile($asset, $tempFile, $asset->getFilename());
                return true;
            }

            return false;

        } catch (\Exception $e) {
            return false;
        } finally {
            if (isset($tempFile) && file_exists($tempFile)) {
                @unlink($tempFile);
            }
        }
    }

    /**
     * Process Office documents - remove author metadata
     */
    private function processOfficeDocument(string $filePath): bool
    {
        $zip = new ZipArchive();
        if ($zip->open($filePath) !== TRUE) {
            return false;
        }

        $xmlContent = $zip->getFromName('docProps/core.xml');
        if ($xmlContent) {
            // Remove author fields (handle attributes too)
            $xmlContent = preg_replace('/<dc:creator[^>]*>.*?<\/dc:creator>/s', '<dc:creator></dc:creator>', $xmlContent);
            $xmlContent = preg_replace('/<dc:lastModifiedBy[^>]*>.*?<\/dc:lastModifiedBy>/s', '<dc:lastModifiedBy></dc:lastModifiedBy>', $xmlContent);

            $zip->deleteName('docProps/core.xml');
            $zip->addFromString('docProps/core.xml', $xmlContent);
        }

        $zip->close();
        return true;
    }

    /**
     * Register event handlers
     */
    private function attachEventHandlers(): void
    {
        Event::on(
            Asset::class,
            Element::EVENT_AFTER_SAVE,
            function (Event $event) {
                /** @var Asset $asset */
                $asset = $event->sender;

                if ($asset->getScenario() === Asset::SCENARIO_CREATE ||
                    $asset->getScenario() === Asset::SCENARIO_FILEOPS) {
                    $this->stripMetadata($asset);
                }
            }
        );
    }
}