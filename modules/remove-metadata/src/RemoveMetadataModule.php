<?php
declare(strict_types=1);

namespace removemetadata;

use Craft;
use craft\base\Element;
use Throwable;
use craft\elements\Asset;
use yii\base\Event;
use yii\base\Module;
use ZipArchive;
use Exception;

class RemoveMetadataModule extends Module
{
    /**
     * @var array Supported document file extensions
     */
    public array $supportedDocumentExtensions = ['pdf', 'docx', 'xlsx', 'doc', 'xls', 'ppt', 'pptx'];

    /**
     * @var array Supported image file extensions
     */
    public array $supportedImageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];

    /**
     * @var array Supported HTML file extensions
     */
    public array $supportedHtmlExtensions = ['html', 'htm'];

    public function init(): void
    {
        parent::init();

        // Set the controllerNamespace based on whether this is a console or web request
        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'removemetadata\\controllers';
        }

        // Enable automatic processing for newly uploaded files
        Event::on(
            Asset::class,
            Element::EVENT_AFTER_SAVE,
            function($e) {
                /** @var Asset $asset */
                $asset = $e->sender;
                // Only process if this is a new asset or if the file was replaced
                if ($asset->getScenario() === Asset::SCENARIO_CREATE || $asset->getScenario() === Asset::SCENARIO_FILEOPS) {
                    $this->stripMetadata($asset);
                }
            }
        );
    }

    /**
     * Alias for stripMetadata to maintain backward compatibility
     */
    public function stripAuthor(Asset $asset): bool
    {
        return $this->stripMetadata($asset);
    }

    /**
     * Strips metadata from an asset file
     *
     * @param Asset $asset The asset to process
     * @return bool True if metadata was successfully stripped, false otherwise
     */
    public function stripMetadata(Asset $asset): bool
    {
        try {
            // Skip if asset is not yet saved or doesn't have a path
            if (!$asset->id || !$asset->getPath()) {
                Craft::info("Skipping asset {$asset->id}: no path", __METHOD__);
                return false;
            }

            $fs = $asset->getVolume()->getFs();
            $path = $asset->getPath();
            if (!$fs->fileExists($path)) {
                Craft::warning("Skipping asset {$asset->id} ({$asset->getFilename()}): file does not exist at {$path} - may be stored in cloud storage", __METHOD__);
                return false;
            }

            $ext = strtolower(pathinfo($asset->getFilename(), PATHINFO_EXTENSION));

            // Check if file type is supported
            if (!in_array($ext, array_merge($this->supportedImageExtensions, $this->supportedHtmlExtensions, $this->supportedDocumentExtensions))) {
                Craft::info("Skipping asset {$asset->id}: unsupported extension {$ext}", __METHOD__);
                return false;
            }

            Craft::info("Processing asset {$asset->id} ({$asset->getFilename()}) with extension {$ext}", __METHOD__);

            $tmp = tempnam(sys_get_temp_dir(), 'mdc');
            file_put_contents($tmp, stream_get_contents($fs->getFileStream($path)));

            // Process based on file type
            if (in_array($ext, $this->supportedImageExtensions)) {
                $this->processImageFile($tmp, $ext);
            } elseif (in_array($ext, $this->supportedHtmlExtensions)) {
                $this->processHtmlFile($tmp);
            } elseif (in_array($ext, $this->supportedDocumentExtensions)) {
                $this->processDocumentFile($tmp, $ext);
            }

            // Check if the processed file is different from the original
            $originalSize = filesize($tmp);

            // Replace the asset with the cleaned version
            Craft::info("Replacing asset {$asset->id} with processed version (size: {$originalSize} bytes)", __METHOD__);
            Craft::$app->getAssets()->replaceAssetFile($asset, $tmp, $asset->getFilename());

            @unlink($tmp);

            Craft::info("Successfully processed asset {$asset->id}", __METHOD__);
            return true;
        } catch (Throwable $e) {
            Craft::error("Error stripping metadata from asset {$asset->id}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    /**
     * Process image files to remove metadata
     *
     * @param string $filePath Path to the temporary file
     * @param string $extension File extension
     * @return void
     */
    private function processImageFile(string $filePath, string $extension): void
    {
        Craft::info("Processing image file with extension: {$extension}", __METHOD__);

        switch ($extension) {
            case 'jpg': case 'jpeg':
                $img = @imagecreatefromjpeg($filePath);
                if ($img) {
                    imagejpeg($img, $filePath, 100);
                    imagedestroy($img);
                    Craft::info("Successfully processed JPEG image", __METHOD__);
                } else {
                    Craft::warning("Failed to create image resource from JPEG", __METHOD__);
                }
                break;
            case 'png':
                $img = @imagecreatefrompng($filePath);
                if ($img) {
                    imagepng($img, $filePath);
                    imagedestroy($img);
                    Craft::info("Successfully processed PNG image", __METHOD__);
                } else {
                    Craft::warning("Failed to create image resource from PNG", __METHOD__);
                }
                break;
            case 'gif':
                $img = @imagecreatefromgif($filePath);
                if ($img) {
                    imagegif($img, $filePath);
                    imagedestroy($img);
                    Craft::info("Successfully processed GIF image", __METHOD__);
                } else {
                    Craft::warning("Failed to create image resource from GIF", __METHOD__);
                }
                break;
            case 'webp':
                $img = @imagecreatefromwebp($filePath);
                if ($img) {
                    imagewebp($img, $filePath);
                    imagedestroy($img);
                    Craft::info("Successfully processed WEBP image", __METHOD__);
                } else {
                    Craft::warning("Failed to create image resource from WEBP", __METHOD__);
                }
                break;
            case 'svg':
                $this->processSvgFile($filePath);
                break;
        }
    }

    /**
     * Process HTML files to remove author metadata
     *
     * @param string $filePath Path to the temporary file
     * @return void
     */
    private function processHtmlFile(string $filePath): void
    {
        $content = file_get_contents($filePath);
        // Remove author meta tags
        $content = preg_replace('/<meta\s+name=["\']author["\'][^>]*>/i', '', $content);
        // Remove generator meta tags
        $content = preg_replace('/<meta\s+name=["\']generator["\'][^>]*>/i', '', $content);
        file_put_contents($filePath, $content);
    }

    /**
     * Process SVG files to remove metadata
     *
     * @param string $filePath Path to the temporary file
     * @return void
     */
    private function processSvgFile(string $filePath): void
    {
        $content = file_get_contents($filePath);
        if ($content === false) {
            Craft::warning("Could not read SVG file: {$filePath}", __METHOD__);
            return;
        }

        // Remove metadata elements from SVG
        $patterns = [
            '/<metadata[^>]*>.*?<\/metadata>/is',
            '/<title[^>]*>.*?<\/title>/is',
            '/<desc[^>]*>.*?<\/desc>/is',
            '/<!--.*?-->/s',
            '/creator="[^"]*"/i',
            '/author="[^"]*"/i',
        ];

        $originalLength = strlen($content);
        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        $newLength = strlen($content);
        if ($newLength !== $originalLength) {
            file_put_contents($filePath, $content);
            Craft::info("Removed SVG metadata, size changed from {$originalLength} to {$newLength} bytes", __METHOD__);
        } else {
            Craft::info("No SVG metadata found to remove", __METHOD__);
        }
    }

    /**
     * Process document files to remove metadata
     *
     * @param string $filePath Path to the temporary file
     * @param string $extension File extension
     * @return void
     */
    private function processDocumentFile(string $filePath, string $extension): void
    {
        switch ($extension) {
            case 'pdf':
                $this->removePdfMetadata($filePath);
                break;

            case 'docx':
            case 'pptx':
            case 'xlsx':
                $this->removeOfficeXmlMetadata($filePath);
                break;

            case 'doc':
            case 'xls':
            case 'ppt':
                // Legacy Office formats are binary and more complex to handle
                // We'll log that they're not fully supported
                Craft::warning("Legacy Office format ($extension) metadata removal is limited", __METHOD__);
                break;
        }
    }

    /**
     * Removes PDF metadata using basic string replacement
     *
     * @param string $filePath
     * @return void
     */
    private function removePdfMetadata(string $filePath): void
    {
        try {
            $content = file_get_contents($filePath);
            if ($content === false) {
                Craft::warning("Could not read PDF file: {$filePath}", __METHOD__);
                return;
            }

            // Remove common metadata fields from PDF
            $patterns = [
                '/\/Author\s*\([^)]*\)/',
                '/\/Creator\s*\([^)]*\)/',
                '/\/Producer\s*\([^)]*\)/',
                '/\/Title\s*\([^)]*\)/',
                '/\/Subject\s*\([^)]*\)/',
                '/\/Keywords\s*\([^)]*\)/',
                '/\/CreationDate\s*\([^)]*\)/',
                '/\/ModDate\s*\([^)]*\)/',
            ];

            $originalSize = strlen($content);
            foreach ($patterns as $pattern) {
                $content = preg_replace($pattern, '', $content);
            }

            $newSize = strlen($content);
            if ($newSize !== $originalSize) {
                file_put_contents($filePath, $content);
                Craft::info("Removed PDF metadata, size changed from {$originalSize} to {$newSize} bytes", __METHOD__);
            } else {
                Craft::info("No PDF metadata found to remove", __METHOD__);
            }
        } catch (Exception $e) {
            Craft::error("Error processing PDF metadata: {$e->getMessage()}", __METHOD__);
        }
    }

    /**
     * Removes metadata from Office XML files (DOCX, XLSX, PPTX)
     *
     * @param string $filePath
     * @return void
     */
    private function removeOfficeXmlMetadata(string $filePath): void
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($filePath) === true) {
                // Remove all metadata files in docProps directory
                $zip->deleteName('docProps/app.xml');
                $zip->deleteName('docProps/core.xml');
                $zip->deleteName('docProps/custom.xml');

                $zip->close();
            } else {
                Craft::warning("Unable to open Office XML file for metadata removal: {$filePath}", __METHOD__);
            }
        } catch (Exception $e) {
            Craft::error("Error processing Office XML metadata: {$e->getMessage()}", __METHOD__);
        }
    }
}