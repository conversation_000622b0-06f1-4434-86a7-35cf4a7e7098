<?php

namespace removemetadata\console\controllers;

use Craft;
use craft\console\Controller;
use craft\elements\Asset;
use removemetadata\RemoveMetadataModule;
use RuntimeException;
use yii\base\InvalidConfigException;
use yii\console\ExitCode;

class MetadataController extends Controller
{
    public ?int $limit = null;

    public function options($actionID): array
    {
        $options = parent::options($actionID);
        $options[] = 'limit';
        return $options;
    }

    /**
     * @throws InvalidConfigException
     */
    public function actionStrip(): int
    {
        try {
            $module = $this->getModule();

            $query = Asset::find()->orderBy(['id' => SORT_ASC]);
            if ($this->limit !== null) {
                $query->limit($this->limit);
            }

            $assets = $query->all();
            if (empty($assets)) {
                $this->stdout("No assets found to process.\n");
                return ExitCode::OK;
            }

            $processed = 0;
            foreach ($assets as $asset) {
                if ($module->stripMetadata($asset)) {
                    $processed++;
                }
            }

            $this->stdout("Processed {$processed} assets.\n");
            return ExitCode::OK;

        } catch (RuntimeException) {
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }

    private function getModule(): RemoveMetadataModule
    {
        $module = Craft::$app->getModule('remove-metadata');
        if (!($module instanceof RemoveMetadataModule)) {
            throw new RuntimeException('remove-metadata module not found or invalid type');
        }
        return $module;
    }
}
