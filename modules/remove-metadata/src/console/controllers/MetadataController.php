<?php

namespace removemetadata\console\controllers;

use Craft;
use craft\console\Controller;
use craft\elements\Asset;
use removemetadata\RemoveMetadataModule;
use RuntimeException;
use yii\base\InvalidConfigException;
use yii\console\ExitCode;

class MetadataController extends Controller
{
    public ?int $limit = null;

    public function options($actionID): array
    {
        $options = parent::options($actionID);
        $options[] = 'limit';
        return $options;
    }

    public function actionStrip(): int
    {
        try {
            $module = $this->getModule();
            $service = $module->getCleanupService();

            $query = Asset::find()->orderBy(['id' => SORT_ASC]);
            if ($this->limit !== null) {
                $query->limit($this->limit);
            }

            $assets = $query->all();
            if (empty($assets)) {
                return ExitCode::OK;
            }

            foreach ($assets as $asset) {
                $service->stripMetadata($asset);
            }

            return ExitCode::OK;

        } catch (RuntimeException) {
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }

    private function getModule(): RemoveMetadataModule
    {
        $module = Craft::$app->getModule('remove-metadata');
        if (!($module instanceof RemoveMetadataModule)) {
            throw new RuntimeException('remove-metadata module not found or invalid type');
        }
        return $module;
    }
}
