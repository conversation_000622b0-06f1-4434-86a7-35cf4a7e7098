<?php

namespace removemetadata\console\controllers;

use Craft;
use craft\console\Controller;
use craft\elements\Asset;
use removemetadata\RemoveMetadataModule;
use yii\console\ExitCode;

class MetadataController extends Controller
{
    public ?int $limit = null;

    public function options($actionID): array
    {
        $options = parent::options($actionID);
        $options[] = 'limit';
        return $options;
    }

    public function actionStrip(): int
    {
        $module = Craft::$app->getModule('remove-metadata');
        if (!($module instanceof RemoveMetadataModule)) {
            $this->stderr("Error: remove-metadata module not found\n");
            return ExitCode::UNSPECIFIED_ERROR;
        }

        $query = Asset::find()->orderBy(['id' => SORT_ASC]);
        if ($this->limit !== null) {
            $query->limit($this->limit);
        }

        $assets = $query->all();
        if (empty($assets)) {
            $this->stdout("No assets found to process.\n");
            return ExitCode::OK;
        }

        $processed = 0;
        foreach ($assets as $asset) {
            if ($module->stripMetadata($asset)) {
                $processed++;
            }
        }

        $this->stdout("Processed {$processed} assets.\n");
        return ExitCode::OK;
    }
}
