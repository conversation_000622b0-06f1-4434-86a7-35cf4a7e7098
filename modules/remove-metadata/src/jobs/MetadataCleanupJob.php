<?php

namespace removemetadata\jobs;

use Craft;
use craft\queue\BaseJob;
use craft\elements\Asset;
use removemetadata\RemoveMetadataModule;

class MetadataCleanupJob extends BaseJob
{
    public int $assetId;

    public function execute($queue): void
    {
        $asset = Asset::findOne($this->assetId);
        if (!$asset) {
            return;
        }

        $module = Craft::$app->getModule('remove-metadata');
        if (!($module instanceof RemoveMetadataModule)) {
            return;
        }

        $module->getCleanupService()->stripMetadata($asset);
    }

    protected function defaultDescription(): string
    {
        return "Strip metadata for asset #{$this->assetId}";
    }
}