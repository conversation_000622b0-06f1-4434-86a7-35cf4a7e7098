<?php

namespace removemetadata\services;

use Craft;
use RuntimeException;
use Throwable;
use craft\elements\Asset;
use setasign\Fpdi\Fpdi;
use yii\base\Component;
use ZipArchive;

class MetadataCleanupService extends Component
{
    /** @var string[] */
    public array $htmlExtensions = ['html', 'htm'];
    public string $pdfExtension = 'pdf';
    /** @var string[] */
    public array $officeExtensions = ['docx', 'xlsx'];
    /** @var string[] */
    public array $imageExtensions = ['svg'];

    public function stripMetadata(Asset $asset): bool
    {
        $temporaryFilePath = tempnam(sys_get_temp_dir(), 'cleanupload_');
        if (!$temporaryFilePath) {
            Craft::warning("Asset {$asset->id}: temp file creation failed", __METHOD__);
            return false;
        }

        try {
            if (!$this->downloadAsset($asset, $temporaryFilePath)) {
                return false;
            }

            $extension = strtolower(pathinfo($asset->getFilename(), PATHINFO_EXTENSION));

            if (!$this->processFile($temporaryFilePath, $extension)) {
                return false;
            }

            Craft::$app->getAssets()->replaceAssetFile($asset, $temporaryFilePath, $asset->getFilename());
            Craft::info("Asset {$asset->id}: metadata stripped", __METHOD__);
            return true;

        } catch (Throwable $exception) {
            Craft::warning("Asset {$asset->id}: strip failed – {$exception->getMessage()}", __METHOD__);
            return false;
        } finally {
            if (file_exists($temporaryFilePath)) {
                @unlink($temporaryFilePath);
            }
        }
    }

    private function downloadAsset(Asset $asset, string $temporaryFilePath): bool
    {
        $filesystem = $asset->getVolume()->getFs();
        $remoteFilePath = $asset->getPath();

        if (!$filesystem->fileExists($remoteFilePath)) {
            Craft::warning("Asset {$asset->id}: not found", __METHOD__);
            return false;
        }

        $stream = $filesystem->getFileStream($remoteFilePath);
        file_put_contents($temporaryFilePath, stream_get_contents($stream));
        fclose($stream);
        return true;
    }

    private function processFile(string $filePath, string $extension): bool
    {
        if (in_array($extension, $this->htmlExtensions, true)) {
            return $this->processHtml($filePath);
        }

        if ($extension === $this->pdfExtension) {
            return $this->processPdf($filePath);
        }

        if (in_array($extension, $this->officeExtensions, true)) {
            return $this->processDocument($filePath);
        }

        if (in_array($extension, $this->imageExtensions, true)) {
            return $this->processSvg($filePath);
        }

        return false;
    }

    private function processHtml(string $filePath): bool
    {
        $htmlContent = file_get_contents($filePath);
        if ($htmlContent === false) {
            return false;
        }

        $cleanedHtml = preg_replace('/<meta\s+name=["\']author["\'][^>]*>/i', '', $htmlContent);
        if ($cleanedHtml === null) {
            throw new RuntimeException('HTML strip failed');
        }

        return file_put_contents($filePath, $cleanedHtml) !== false;
    }

    private function processPdf(string $filePath): bool
    {
        try {
            $pdfDocument = new Fpdi();
            $pageCount = $pdfDocument->setSourceFile($filePath);

            for ($pageNumber = 1; $pageNumber <= $pageCount; $pageNumber++) {
                $templateId = $pdfDocument->importPage($pageNumber);
                $size = $pdfDocument->getTemplateSize($templateId);

                if (!is_array($size)) {
                    return false;
                }

                if (!isset($size['orientation'], $size['width'], $size['height'])) {
                    return false;
                }

                $pdfDocument->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdfDocument->useTemplate($templateId);
            }

            $pdfDocument->SetAuthor('');

            return $pdfDocument->Output($filePath, 'F') !== false;
        } catch (\Exception $e) {
            Craft::error("Error removing PDF author metadata for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processDocument(string $filePath): bool
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                $xmlContent = $zip->getFromName('docProps/core.xml');
                if ($xmlContent) {
                    // Only replace the creator (author) field
                    $xmlContent = preg_replace('/<dc:creator>.*?<\/dc:creator>/', '<dc:creator></dc:creator>', $xmlContent);
                    $zip->deleteName('docProps/core.xml');
                    $zip->addFromString('docProps/core.xml', $xmlContent);
                }
                $zip->close();
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Craft::error("Failed to remove office document author metadata for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processSvg(string $filePath): bool
    {
        try {
            $svgContent = file_get_contents($filePath);
            if ($svgContent === false) {
                return false;
            }

            $cleanedContent = preg_replace('/<metadata[^>]*>.*?author.*?<\/metadata>/si', '', $svgContent);

            if (file_put_contents($filePath, $cleanedContent) !== false) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Craft::error("Failed to remove SVG author metadata for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }
}