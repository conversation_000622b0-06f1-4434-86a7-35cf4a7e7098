<?php

namespace removemetadata\services;

use Craft;
use RuntimeException;
use Throwable;
use craft\elements\Asset;
use setasign\Fpdi\Fpdi;
use yii\base\Component;
use ZipArchive;

class MetadataCleanupService extends Component
{
    /** @var string[] */
    public array $htmlExtensions = ['html', 'htm'];
    public string $pdfExtension = 'pdf';
    /** @var string[] */
    public array $officeExtensions = ['docx', 'xlsx', 'pptx'];
    /** @var string[] */
    public array $legacyOfficeExtensions = ['doc', 'xls', 'ppt'];
    /** @var string[] */
    public array $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'tiff', 'tif'];

    public function stripMetadata(Asset $asset): bool
    {
        $temporaryFilePath = tempnam(sys_get_temp_dir(), 'cleanupload_');
        if (!$temporaryFilePath) {
            Craft::warning("Asset {$asset->id}: temp file creation failed", __METHOD__);
            return false;
        }

        try {
            if (!$this->downloadAsset($asset, $temporaryFilePath)) {
                return false;
            }

            $extension = strtolower(pathinfo($asset->getFilename(), PATHINFO_EXTENSION));

            if (!$this->processFile($temporaryFilePath, $extension)) {
                return false;
            }

            Craft::$app->getAssets()->replaceAssetFile($asset, $temporaryFilePath, $asset->getFilename());
            Craft::info("Asset {$asset->id}: metadata stripped", __METHOD__);
            return true;

        } catch (Throwable $exception) {
            Craft::warning("Asset {$asset->id}: strip failed – {$exception->getMessage()}", __METHOD__);
            return false;
        } finally {
            if (file_exists($temporaryFilePath)) {
                @unlink($temporaryFilePath);
            }
        }
    }

    private function downloadAsset(Asset $asset, string $temporaryFilePath): bool
    {
        $filesystem = $asset->getVolume()->getFs();
        $remoteFilePath = $asset->getPath();

        if (!$filesystem->fileExists($remoteFilePath)) {
            Craft::warning("Asset {$asset->id}: not found", __METHOD__);
            return false;
        }

        $stream = $filesystem->getFileStream($remoteFilePath);
        file_put_contents($temporaryFilePath, stream_get_contents($stream));
        fclose($stream);
        return true;
    }

    private function processFile(string $filePath, string $extension): bool
    {
        if (in_array($extension, $this->htmlExtensions, true)) {
            return $this->processHtml($filePath);
        }

        if ($extension === $this->pdfExtension) {
            return $this->processPdf($filePath);
        }

        if (in_array($extension, $this->officeExtensions, true)) {
            return $this->processDocument($filePath);
        }

        if (in_array($extension, $this->legacyOfficeExtensions, true)) {
            return $this->processLegacyOfficeDocument($filePath, $extension);
        }

        if (in_array($extension, $this->imageExtensions, true)) {
            return $this->processImage($filePath, $extension);
        }

        // Log unsupported file types
        Craft::info("Asset with extension '{$extension}' is not supported for metadata removal", __METHOD__);
        return false;
    }

    private function processHtml(string $filePath): bool
    {
        $htmlContent = file_get_contents($filePath);
        if ($htmlContent === false) {
            Craft::warning("Failed to read HTML file: {$filePath}", __METHOD__);
            return false;
        }

        // Remove various metadata tags
        $patterns = [
            '/<meta\s+name=["\']author["\'][^>]*>/i',
            '/<meta\s+name=["\']generator["\'][^>]*>/i',
            '/<meta\s+name=["\']creator["\'][^>]*>/i',
            '/<meta\s+name=["\']publisher["\'][^>]*>/i',
            '/<meta\s+name=["\']copyright["\'][^>]*>/i',
            '/<meta\s+name=["\']company["\'][^>]*>/i',
            '/<meta\s+name=["\']application-name["\'][^>]*>/i',
            '/<!--.*?-->/s', // Remove HTML comments
        ];

        foreach ($patterns as $pattern) {
            $htmlContent = preg_replace($pattern, '', $htmlContent);
            if ($htmlContent === null) {
                throw new RuntimeException('HTML metadata removal failed');
            }
        }

        $result = file_put_contents($filePath, $htmlContent);
        if ($result !== false) {
            Craft::info("Successfully processed HTML metadata", __METHOD__);
            return true;
        } else {
            Craft::warning("Failed to write processed HTML file", __METHOD__);
            return false;
        }
    }

    private function processPdf(string $filePath): bool
    {
        try {
            $pdfDocument = new Fpdi();
            $pageCount = $pdfDocument->setSourceFile($filePath);

            for ($pageNumber = 1; $pageNumber <= $pageCount; $pageNumber++) {
                $templateId = $pdfDocument->importPage($pageNumber);
                $size = $pdfDocument->getTemplateSize($templateId);

                if (!is_array($size)) {
                    Craft::warning("Failed to get template size for PDF page {$pageNumber}", __METHOD__);
                    return false;
                }

                if (!isset($size['orientation'], $size['width'], $size['height'])) {
                    Craft::warning("Invalid template size data for PDF page {$pageNumber}", __METHOD__);
                    return false;
                }

                $pdfDocument->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdfDocument->useTemplate($templateId);
            }

            // Clear all metadata fields
            $pdfDocument->SetAuthor('');
            $pdfDocument->SetCreator('');
            $pdfDocument->SetTitle('');
            $pdfDocument->SetSubject('');
            $pdfDocument->SetKeywords('');

            // Set minimal producer info
            $pdfDocument->SetProducer('');

            $result = $pdfDocument->Output($filePath, 'F');
            if ($result !== false) {
                Craft::info("Successfully processed PDF metadata", __METHOD__);
                return true;
            } else {
                Craft::warning("Failed to output processed PDF", __METHOD__);
                return false;
            }
        } catch (\Exception $e) {
            Craft::error("Error removing PDF metadata for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processDocument(string $filePath): bool
    {
        try {
            $zip = new ZipArchive();
            if ($zip->open($filePath) === TRUE) {
                // Process core.xml (contains author, creator, etc.)
                $this->processDocumentCoreXml($zip);

                // Process app.xml (contains application info)
                $this->processDocumentAppXml($zip);

                // Process custom.xml (contains custom properties)
                $this->processDocumentCustomXml($zip);

                $zip->close();
                Craft::info("Successfully processed Office document metadata", __METHOD__);
                return true;
            }
            Craft::warning("Failed to open Office document as ZIP archive", __METHOD__);
            return false;
        } catch (\Exception $e) {
            Craft::error("Failed to remove office document metadata for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processDocumentCoreXml(ZipArchive $zip): void
    {
        $xmlContent = $zip->getFromName('docProps/core.xml');
        if ($xmlContent) {
            // Remove various metadata fields
            $patterns = [
                '/<dc:creator>.*?<\/dc:creator>/' => '<dc:creator></dc:creator>',
                '/<dc:lastModifiedBy>.*?<\/dc:lastModifiedBy>/' => '<dc:lastModifiedBy></dc:lastModifiedBy>',
                '/<dc:title>.*?<\/dc:title>/' => '<dc:title></dc:title>',
                '/<dc:description>.*?<\/dc:description>/' => '<dc:description></dc:description>',
                '/<dc:subject>.*?<\/dc:subject>/' => '<dc:subject></dc:subject>',
                '/<cp:keywords>.*?<\/cp:keywords>/' => '<cp:keywords></cp:keywords>',
                '/<cp:category>.*?<\/cp:category>/' => '<cp:category></cp:category>',
                '/<cp:revision>.*?<\/cp:revision>/' => '<cp:revision>1</cp:revision>',
            ];

            foreach ($patterns as $pattern => $replacement) {
                $xmlContent = preg_replace($pattern, $replacement, $xmlContent);
            }

            $zip->deleteName('docProps/core.xml');
            $zip->addFromString('docProps/core.xml', $xmlContent);
        }
    }

    private function processDocumentAppXml(ZipArchive $zip): void
    {
        $xmlContent = $zip->getFromName('docProps/app.xml');
        if ($xmlContent) {
            // Remove application-specific metadata
            $patterns = [
                '/<Application>.*?<\/Application>/' => '<Application></Application>',
                '/<AppVersion>.*?<\/AppVersion>/' => '<AppVersion></AppVersion>',
                '/<Company>.*?<\/Company>/' => '<Company></Company>',
                '/<Manager>.*?<\/Manager>/' => '<Manager></Manager>',
            ];

            foreach ($patterns as $pattern => $replacement) {
                $xmlContent = preg_replace($pattern, $replacement, $xmlContent);
            }

            $zip->deleteName('docProps/app.xml');
            $zip->addFromString('docProps/app.xml', $xmlContent);
        }
    }

    private function processDocumentCustomXml(ZipArchive $zip): void
    {
        // Remove custom properties entirely as they may contain sensitive information
        if ($zip->getFromName('docProps/custom.xml')) {
            $zip->deleteName('docProps/custom.xml');
        }
    }

    private function processLegacyOfficeDocument(string $filePath, string $extension): bool
    {
        try {
            // For legacy Office formats, we can try using exiftool if available
            if ($this->isExiftoolAvailable()) {
                return $this->processImageWithExiftool($filePath);
            } else {
                // Legacy Office formats are binary and complex to handle without specialized tools
                Craft::warning("Legacy Office format ({$extension}) metadata removal requires exiftool, which is not available", __METHOD__);
                return false;
            }
        } catch (\Exception $e) {
            Craft::error("Failed to process legacy Office document for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processImage(string $filePath, string $extension): bool
    {
        try {
            if ($extension === 'svg') {
                return $this->processSvg($filePath);
            }

            // For other image formats, use exiftool if available, otherwise try basic processing
            if ($this->isExiftoolAvailable()) {
                return $this->processImageWithExiftool($filePath);
            } else {
                return $this->processImageBasic($filePath, $extension);
            }
        } catch (\Exception $e) {
            Craft::error("Failed to remove image metadata for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processSvg(string $filePath): bool
    {
        try {
            $svgContent = file_get_contents($filePath);
            if ($svgContent === false) {
                return false;
            }

            // Remove various metadata elements from SVG
            $patterns = [
                '/<metadata[^>]*>.*?<\/metadata>/si',
                '/<title[^>]*>.*?<\/title>/si',
                '/<desc[^>]*>.*?<\/desc>/si',
                '/<!--.*?-->/s',
                '/creator="[^"]*"/i',
                '/author="[^"]*"/i',
                '/generator="[^"]*"/i',
            ];

            foreach ($patterns as $pattern) {
                $svgContent = preg_replace($pattern, '', $svgContent);
            }

            return file_put_contents($filePath, $svgContent) !== false;
        } catch (\Exception $e) {
            Craft::error("Failed to remove SVG metadata for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function isExiftoolAvailable(): bool
    {
        $output = [];
        $returnCode = 0;
        exec('which exiftool 2>/dev/null', $output, $returnCode);
        return $returnCode === 0;
    }

    private function processImageWithExiftool(string $filePath): bool
    {
        try {
            // Use exiftool to remove all metadata
            $command = sprintf('exiftool -all= -overwrite_original %s 2>/dev/null', escapeshellarg($filePath));
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                Craft::info("Successfully removed metadata using exiftool for {$filePath}", __METHOD__);
                return true;
            } else {
                Craft::warning("Exiftool failed for {$filePath}, return code: {$returnCode}", __METHOD__);
                return false;
            }
        } catch (\Exception $e) {
            Craft::error("Failed to process image with exiftool for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processImageBasic(string $filePath, string $extension): bool
    {
        try {
            // Basic image processing without exiftool
            // This is limited but better than nothing
            switch (strtolower($extension)) {
                case 'jpg':
                case 'jpeg':
                    return $this->processJpegBasic($filePath);
                case 'png':
                    return $this->processPngBasic($filePath);
                default:
                    Craft::info("Basic processing not available for {$extension} files", __METHOD__);
                    return false;
            }
        } catch (\Exception $e) {
            Craft::error("Failed basic image processing for {$filePath}: {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    private function processJpegBasic(string $filePath): bool
    {
        // For JPEG, we can try to recreate the image without metadata
        $image = imagecreatefromjpeg($filePath);
        if ($image === false) {
            return false;
        }

        $result = imagejpeg($image, $filePath, 90);
        imagedestroy($image);

        return $result;
    }

    private function processPngBasic(string $filePath): bool
    {
        // For PNG, recreate the image to remove metadata
        $image = imagecreatefrompng($filePath);
        if ($image === false) {
            return false;
        }

        // Preserve transparency
        imagealphablending($image, false);
        imagesavealpha($image, true);

        $result = imagepng($image, $filePath);
        imagedestroy($image);

        return $result;
    }
}