<?php

declare(strict_types=1);

namespace modules\documentresolver;

use Craft;
use yii\base\Module as BaseModule;

/**
 * DocumentResolver module
 *
 * @method static DocumentResolver getInstance()
 */
class DocumentResolver extends BaseModule
{
    public function init(): void
    {
        Craft::setAlias('@modules/documentresolver', __DIR__);

        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'modules\\documentresolver\\console\\controllers';
        } else {
            $this->controllerNamespace = 'modules\\documentresolver\\controllers';
        }

        parent::init();
    }
}
