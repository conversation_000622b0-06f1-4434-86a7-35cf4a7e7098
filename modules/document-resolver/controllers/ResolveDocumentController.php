<?php

declare(strict_types=1);

namespace modules\documentresolver\controllers;

use Craft;
use craft\web\Controller;
use yii\web\Response;

/**
 * Resolve Document controller
 */
class ResolveDocumentController extends Controller
{
    public $defaultAction = 'index';
    protected array|int|bool $allowAnonymous = self::ALLOW_ANONYMOUS_LIVE;

    /**
     * document-resolver/resolve-document action
     */
    public function actionIndex(?string $path): Response
    {
        $assetPath = Craft::getAlias('@documentsPath') . '/' . $path;
        if (file_exists($assetPath)) {
            return $this->response->sendFile($assetPath, null, ["inline" => true]);
        }

        return $this->response->setStatusCode(404);
    }
}
