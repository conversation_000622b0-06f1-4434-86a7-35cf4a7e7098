<?php

namespace organization\actionHandlers;

use organization\models\Organization;
use organization\actions\ActionInterface;

class FetchDataActionHandler
{
    /**
     * @param iterable<ActionInterface> $actions
     */
    public function __construct(private readonly iterable $actions)
    {
    }

    public function handle(Organization $organization): void
    {
        foreach ($this->actions as $action) {
            $action->execute($organization);
        }
    }
}