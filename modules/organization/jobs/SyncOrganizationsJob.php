<?php

declare(strict_types=1);

namespace organization\jobs;

use \Craft;
use craft\elements\Entry;
use craft\errors\ElementNotFoundException;
use craft\queue\BaseJob;
use organization\actionHandlers\FetchDataActionHandler;
use organization\models\Organization;
use organization\repository\wozit\WozItRepository;
use organization\services\OrganizationService;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\di\NotInstantiableException;
use yii\log\Logger;

class SyncOrganizationsJob extends BaseJob
{
    /** @param Entry[] $organizationEntries */
    public function __construct(private readonly array $organizationEntries)
    {
        parent::__construct();
    }

    /**
     * @throws ElementNotFoundException
     * @throws NotInstantiableException
     * @throws \Throwable
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function execute($queue): void
    {
        /** @var FetchDataActionHandler $actionHandler */
        $actionHandler = Craft::$container->get(FetchDataActionHandler::class);

        $i = 0;
        $total = count($this->organizationEntries);

        foreach ($this->organizationEntries as $organizationEntry) {

            try {
                /** @var Organization $organization */
                $organization = $this->updateEntry($organizationEntry);

                $this->addCachedData($organization);

                $actionHandler->handle($organization);
            } catch (\Exception $e) {
                Craft::getLogger()->log($e->getMessage(), Logger::LEVEL_WARNING);
                throw $e;
            }

            $this->setProgress(
                $queue,
                $i / $total,
                Craft::t('app', '{step, number} of {total, number}', [
                    'step' => $i++,
                    'total' => $total,
                ])
            );
        }
    }

    protected function defaultDescription(): string
    {
        return 'Syncing organizations';
    }

    /**
     * @throws ElementNotFoundException
     * @throws NotInstantiableException
     * @throws InvalidConfigException
     * @throws \Throwable
     * @throws Exception
     */
    private function updateEntry(Entry $organization): Organization
    {
        /** @var OrganizationService $organizationService */
        $organizationService = Craft::$container->get(OrganizationService::class);

        /** @var WozItRepository $wozItRepository */
        $wozItRepository = Craft::$container->get(WozItRepository::class);

        try {
            $organizationService->save($wozItRepository->getOrganization($organization->externalId));
        } catch (\Exception $e) {
            Craft::getLogger()->log($e->getMessage(), Logger::LEVEL_WARNING);
            throw $e;
        }

        return $organizationService->fetchByEntry($organization);
    }

    private function addCachedData(Organization $organization): void
    {
        if (Craft::$app->getCache()->exists($organization->getIdentifier())) {
            $cachedOrganization = Craft::$app->getCache()->get($organization->getIdentifier());
            $organization->override($cachedOrganization);
        }
    }
}
