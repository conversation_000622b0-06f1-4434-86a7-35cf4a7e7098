<?php

namespace organization\actions;

use Doctrine\Common\Collections\ArrayCollection;
use organization\models\Chart;
use organization\models\Organization;
use organization\repository\wozit\WozItRepository;

class ChartFeaturesAction implements ActionInterface
{
    public function __construct(private readonly WozItRepository $wozitRepository)
    {
    }

    public function execute(Organization $organization): void
    {
        /** @var ArrayCollection<string, Chart> $charts */
        $charts = $organization->getCharts();

        $organization->resetCharts();

        try {
            $this->wozitRepository->getChartFeatures($organization);
        } catch (\Exception | \TypeError $e) {
            $this->rollback($organization, $charts);
        }
    }

    /**
     * @param ArrayCollection<string, Chart> $charts
     */
    private function rollback(Organization $organization, ArrayCollection $charts): void
    {
        if ($charts->count() > 0) {
            array_map(fn($chart) => $organization->addChart($chart), $charts->toArray());
        }
    }
}
