<?php

namespace organization\actions;

use organization\models\Organization;

use organization\repository\wozit\WozItRepository;

class LogoAction implements ActionInterface
{
    public function __construct(private readonly WozItRepository $wozitRepository)
    {
    }

    public function execute(Organization $organization): void
    {
        $logo = $organization->getLogo() ?? '';
    
        try {
            $this->wozitRepository->getLogo($organization);
        } catch (\Exception | \TypeError $e) {
            $this->rollback($organization, $logo);
        }
    }

    private function rollback(Organization $organization, string $logo): void
    {
        $organization->addLogo($logo);
    }
}
