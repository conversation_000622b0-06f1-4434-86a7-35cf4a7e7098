<?php

namespace organization\actions;

use organization\models\Organization;
use organization\models\Research;
use organization\repository\wozit\WozItRepository;

class ResearchAction implements ActionInterface
{
    public function __construct(private readonly WozItRepository $wozitRepository)
    {
    }

    public function execute(Organization $organization): void
    {
        $researches = $organization->getResearches();

        $organization->resetResearches();

        try {
            $this->wozitRepository->getResearches($organization);
        } catch (\Exception | \TypeError $e) {
            $this->rollback($organization, $researches);
        }
    }

    /**
     * @param Research[] $researches
     */
    private function rollback(Organization $organization, array $researches): void 
    {
        array_map(fn($research) => $organization->addResearch($research), $researches);
    }
}
