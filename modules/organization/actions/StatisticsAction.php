<?php

namespace organization\actions;

use organization\models\Organization;
use organization\models\Statistics;
use organization\repository\wozit\WozItRepository;

class StatisticsAction implements ActionInterface
{
    public function __construct(private readonly WozItRepository $wozitRepository)
    {
    }

    public function execute(Organization $organization): void
    {
        $statistics = $organization->getStatistics();

        $organization->resetStatistics();

        try {
            $this->wozitRepository->getStatistics($organization);
        } catch (\Exception | \TypeError $e) {
            $this->rollback($organization, $statistics);
        }
    }

    /**
     * @param Statistics[] $statistics
     */
    private function rollback(Organization $organization, array $statistics): void
    {
        array_map(fn($statistic) => $organization->addStatistics($statistic), $statistics);
    }
}
