<?php

namespace organization\actions;

use organization\models\Information;
use organization\models\Organization;
use organization\repository\wozit\WozItRepository;

class PublicInformationAction implements ActionInterface
{
    public function __construct(private readonly WozItRepository $wozitRepository)
    {
    }

    public function execute(Organization $organization): void
    {
        $information = $organization->getInformation();

        $organization->resetInformation();

        try {
            $this->wozitRepository->getPublicFeatures($organization);
        } catch (\Exception | \TypeError $e) {
            $this->rollback($organization, $information);
        }
    }

    /**
     * @param Information[] $information
     */
    private function rollback(Organization $organization, array $information): void
    {
        array_map(fn($info) => $organization->addInformation($info), $information);
    }
}
