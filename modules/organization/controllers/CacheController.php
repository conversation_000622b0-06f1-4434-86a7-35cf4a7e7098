<?php

declare(strict_types=1);

namespace organization\controllers;

use Craft;
use craft\elements\Entry;
use craft\helpers\Queue;
use craft\web\Controller;
use organization\jobs\SyncOrganizationsJob;
use organization\repository\OrganizationRepository;
use yii\web\Response;

class CacheController extends Controller
{
    protected array|int|bool $allowAnonymous = self::ALLOW_ANONYMOUS_NEVER;

    public $defaultAction = 'index';

    public function actionIndex(): Response
    {
        $this->requireCpRequest();

        if (Craft::$app->request->isPost) {

            /** @var array<int> $identifiers */
            /** @phpstan-ignore-next-line */
            $identifiers = Craft::$app->request->getBodyParam('organizations');

            if (empty($identifiers)) {
                $this->redirect('organization/cache-clear');
            }

            /** @var OrganizationRepository $organizationRepository */
            $organizationRepository = Craft::$container->get(OrganizationRepository::class);

            /** @var Entry[] $selectedOrganizations */
            $selectedOrganizations = $organizationRepository->findByIdentifiers($identifiers);

            foreach (array_chunk($selectedOrganizations, 10) as $organizations) {
                Queue::push(new SyncOrganizationsJob($organizations));
            }

            $this->redirect('utilities/queue-manager');
        }

        return $this->renderTemplate('organization/cache-clear');
    }
}
