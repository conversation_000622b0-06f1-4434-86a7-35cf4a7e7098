<?php

namespace organization\controllers;

use Craft;
use Exception;
use craft\web\Controller;
use craft\web\View;
use organization\repository\OrganizationRepository;
use organization\services\EmailService;
use organization\services\NationalAverageService;
use organization\services\OrganizationService;
use yii\base\InvalidConfigException;
use yii\di\NotInstantiableException;
use yii\web\BadRequestHttpException;
use yii\web\Response;

class OrganizationController extends Controller
{
    protected array|int|bool $allowAnonymous = self::ALLOW_ANONYMOUS_LIVE;


    /**
     * @throws NotInstantiableException
     * @throws InvalidConfigException
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function actionDetail(string $entrySlug): Response
    {
        $emailService = EmailService::getInstance();
        
        try {
            $this->requireSiteRequest();

            /** @var OrganizationRepository $organizationRepository */
            $organizationRepository = Craft::$container->get(OrganizationRepository::class);

            /** @var OrganizationService $organizationService */
            $organizationService = Craft::$container->get(OrganizationService::class);

            /** @var NationalAverageService $nationalAverageService */
            $nationalAverageService = Craft::$container->get(NationalAverageService::class);

            $entry = $organizationRepository->findBySlug($entrySlug);

            if (!$entry) {
                throw new Exception("Organization not found for slug: $entrySlug");
            }

            $organization = $organizationService->fetchByEntry($entry);

            $associatedOrganizations = [];
            $affiliated = null;

            if ($organization->isAssociation() && !empty($organization->getAssociatedWith())) {
                $associatedOrganizations = $organizationService->fetchAssociated(
                    $organization->getAssociatedWith()
                );
            }

            if (!empty($organization->getAssociated()) && !$organization->isAssociation()) {
                $affiliated = $organizationService->fetchAffiliated(
                    $organization->getAssociated()
                );
            }

            return $this->renderTemplate(
                'organization/_entry',
                [
                    'entry' => $entry,
                    'organization' => $organization,
                    'associatedOrganizations' => $associatedOrganizations,
                    'nationalAverage' => $nationalAverageService->fetch(),
                    'affiliated' => $affiliated
                ],
                View::TEMPLATE_MODE_SITE
            );

        } catch (InvalidConfigException $e) {
            $emailService->addErrorMessage('Configuration error', $e);
            throw $e;
        } catch (Exception $e) {
            $emailService->addErrorMessage('Unexpected error', $e);
            throw $e;
        }
    }
}
