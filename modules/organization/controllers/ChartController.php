<?php

namespace organization\controllers;

use Craft;
use craft\web\Controller;
use yii\web\Response;
use organization\models\Organization;
use organization\models\Chart;
use organization\services\ChartService;

class ChartController extends Controller
{
    protected array|int|bool $allowAnonymous = self::ALLOW_ANONYMOUS_LIVE;

    public function actionDownload(int $organizationId, string $chartCode): Response
    {
        $this->requireSiteRequest();

        /** @var Organization $organization */
        $organization = Craft::$app->getCache()->get($organizationId);

        if (false === $organization) {
            throw new \Exception('Organization not found for key: ' . $organizationId);
        }

        /** @var Chart $chart */
        $chart = $organization->getCharts()->get($chartCode);

        if (false === $chart) {
            throw new \Exception('Chart not found for code: ' . $chartCode);
        }

        /** @var ChartService $chartService */
        $chartService = Craft::$container->get(ChartService::class);
        $chartService->download($chart);

        return (new Response());
    }
}
