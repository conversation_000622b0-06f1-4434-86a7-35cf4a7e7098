<?php

namespace organization\controllers;

use \Craft;
use craft\web\Controller;
use organization\repository\wozit\WozItRepository;
use yii\web\Response;

class DocumentController extends Controller
{
    protected array|int|bool $allowAnonymous = self::ALLOW_ANONYMOUS_LIVE;

    public $defaultAction = 'download';

    public function actionDownload(int $documentId): Response
    {
        $this->requireSiteRequest();

        /** @var WozItRepository $wozitRepository */
        $wozitRepository = Craft::$container->get(WozItRepository::class);
        $wozitRepository->downloadDocument($documentId);

        return (new Response());
    }
}
