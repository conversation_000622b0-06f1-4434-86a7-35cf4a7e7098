{% extends "_layouts/cp.twig" %}

{% set title = "Gemeente cache" %}

{% set fullPageForm = true %}
{% set organizations = craft.entries().section('organization').all() | sort((a, b) => a.title <=> b.title) %}

{% block content %}
	{{ actionInput('organization/cache') }}

    <script language="javascript">
        function toggleAll(source) {
          checkboxes = document.getElementsByName('organizations[]');
          for (var checkbox of checkboxes) {
            checkbox.checked = source.checked;
          }
        }
    </script>

    <p>
        <strong>Selecteer hier onder de gemeentes waarvan je cache wilt verversen.</strong> <br />
        Klik vervolgens op <strong>save</strong>, hierna word je door verwezen naar de <a href="{{ cpUrl('utilities/queue-manager') }}">job queue</a>.
    </p>

    <input type="checkbox" onClick="toggleAll(this)" id="toggle-all" class="checkbox">
    <label for="toggle-all"><strong>Selecteer alles</strong></label>

    {% for organization in organizations %}
        <div>
            <input type="checkbox" id="organization-{{ organization.externalId }}" class="checkbox" name="organizations[]" value="{{ organization.externalId }}">
            <label for="organization-{{ organization.externalId }}">
                {{ organization.title }}
            </label>
        </div>
    {% endfor %}

{% endblock %}
