<?php

namespace organization\factories;

use craft\elements\Entry;
use craft\helpers\StringHelper;
use organization\models\Organization;
use craft\elements\Asset;
use organization\helpers\OrganizationNameHelper;

class OrganizationFactory
{
    /**
     * @param array{'id': float, 'naam': string, 'website': string, 'aangesloten': array, 'aangeslotenbij': int, 'code': string, 'organisatie-type': string, 'algemeenoordeel': float, 'statusinstemming': string} $organization
     * @phpstan-ignore-next-line
     */
    public static function build(array $organization): Organization
    {
        $associatedWith = [];
        if (!empty($organization['aangesloten'])) {
            $associatedWith = array_map(fn ($x) => $x['id'], $organization['aangesloten']);
        }

        $organizationName = OrganizationNameHelper::convert($organization);

        return new Organization(
            $organization['id'],
            $organization['naam'],
            $organizationName,
            $organization['code'],
            $organization['organisatie-type'],
            \Craft::$app->sites->currentSite->getBaseUrl() . 'gemeente/' . StringHelper::slugify($organizationName),
            $organization['algemeenoordeel'],
            self::websiteUrl($organization['website']),
            $associatedWith,
            $organization['aangeslotenbij'],
            $organization['statusinstemming'],
            null,
            null
        );
    }

    public static function buildFromEntry(Entry $entry): Organization
    {
        return new Organization(
            $entry->externalId,
            $entry->title,
            $entry->organizationName,
            $entry->code,
            $entry->organizationType,
            $entry->route,
            $entry->rating,
            $entry->website,
            array_map('intval', explode(',', $entry->associatedWith)),
            $entry->associated,
            $entry->statusConsent,
            self::logoPath($entry->logo->all()),
            $entry->logohash
        );
    }

    /**
     * @param array<Asset> $assets
     */
    public static function addLogo(Organization $organization, array $assets): void
    {
        $organization->addLogo(self::logoPath($assets));
    }

    private static function websiteUrl(?string $website): ?string
    {
        if (null === $website) {
            return null;
        }

        $parseUrl = parse_url($website);

        if (isset($parseUrl['scheme'])) {
            return $website;
        }

        return 'https://' . $website;
    }

    /**
     * @param Asset[] $logos
     */
    private static function logoPath(array $logos = []): ?string
    {
        if (empty($logos)) {
            return null;
        }

        /** @var Asset $logo */
        $logo = array_shift($logos);

        return $logo->getUrl();
    }
}
