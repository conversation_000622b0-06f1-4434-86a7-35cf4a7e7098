<?php

declare(strict_types=1);

namespace organization\ioc;

use \Craft;
use organization\actionHandlers\FetchDataActionHandler;
use organization\repository\OrganizationRepository;
use organization\repository\wozit\WozItRepository;
use organization\services\NationalAverageService;
use organization\services\OrganizationService;
use organization\actions\CacheOrganizationAction;
use organization\actions\ChartFeaturesAction;
use organization\actions\PublicInformationAction;
use organization\actions\ResearchAction;
use organization\actions\LogoAction;
use organization\actions\StatisticsAction;
use organization\repository\AssetRepository;
use organization\repository\VolumeFolderRepository;
use organization\services\CacheService;
use organization\services\ChartService;
use organization\services\DocumentService;
use organization\services\LogoService;

class Container
{
    public function inject(): void
    {
        $this->injectRepositories();
        $this->injectServices();
        $this->injectActions();
        $this->injectActionHandlers();
    }

    private function injectRepositories(): void
    {
        Craft::$container->set(AssetRepository::class, function ($container, $params, $config) {
            return new AssetRepository();
        });

        Craft::$container->set(OrganizationRepository::class, function ($container, $params, $config) {
            return new OrganizationRepository();
        });

        Craft::$container->set(WozItRepository::class, function ($container, $params, $config) {
            return new WozItRepository();
        });

        Craft::$container->set(VolumeFolderRepository::class, function ($container, $params, $config) {
            return new VolumeFolderRepository();
        });
    }

    private function injectServices(): void
    {
        Craft::$container->set(NationalAverageService::class, function ($container, $params, $config) {
            return new NationalAverageService($container->get(WozItRepository::class));
        });

        Craft::$container->set(OrganizationService::class, function ($container, $params, $config) {
            return new OrganizationService(
                $container->get(WozItRepository::class),
                $container->get(OrganizationRepository::class),
                $container->get(VolumeFolderRepository::class),
                $container->get(NationalAverageService::class)
            );
        });

        Craft::$container->set(ChartService::class, function ($container, $params, $config) {
            return new ChartService();
        });

        Craft::$container->set(DocumentService::class, function ($container, $params, $config) {
            return new DocumentService();
        });

        Craft::$container->set(LogoService::class, function ($container, $params, $config) {
            return new LogoService();
        });

        Craft::$container->set(CacheService::class, function ($container, $params, $config) {
            return new CacheService();
        });
    }

    private function injectActions(): void
    {
        Craft::$container->set(CacheOrganizationAction::class, function ($container, $params, $config) {
            return new CacheOrganizationAction();
        });

        Craft::$container->set(ChartFeaturesAction::class, function ($container, $params, $config) {
            return new ChartFeaturesAction($container->get(WozItRepository::class));
        });

        Craft::$container->set(PublicInformationAction::class, function ($container, $params, $config) {
            return new PublicInformationAction($container->get(WozItRepository::class));
        });

        Craft::$container->set(StatisticsAction::class, function ($container, $params, $config) {
            return new StatisticsAction($container->get(WozItRepository::class));
        });

        Craft::$container->set(ResearchAction::class, function ($container, $params, $config) {
            return new ResearchAction($container->get(WozItRepository::class));
        });

        Craft::$container->set(LogoAction::class, function ($container, $params, $config) {
            return new LogoAction($container->get(WozItRepository::class));
        });
    }

    private function injectActionHandlers(): void
    {
        Craft::$container->set(FetchDataActionHandler::class, function ($container, $params, $config) {
            $actions = [
                $container->get(CacheOrganizationAction::class),
                $container->get(ChartFeaturesAction::class),
                $container->get(CacheOrganizationAction::class),
                $container->get(PublicInformationAction::class),
                $container->get(CacheOrganizationAction::class),
                $container->get(StatisticsAction::class),
                $container->get(CacheOrganizationAction::class),
                $container->get(ResearchAction::class),
                $container->get(CacheOrganizationAction::class),
                $container->get(LogoAction::class),
                $container->get(CacheOrganizationAction::class)
            ];

            return new FetchDataActionHandler($actions);
        });
    }
}
