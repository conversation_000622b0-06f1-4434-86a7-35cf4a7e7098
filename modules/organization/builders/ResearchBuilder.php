<?php

namespace organization\builders;

use organization\models\Document;
use organization\models\Research;

class ResearchBuilder
{
    private Research $research;

    /**
     * @param array{'onderzoekid': float, 'onderzoeknaam': string, 'documentnaam': string} $research
     */
    public function __construct(array $research)
    {
        $this->research = new Research(
            $research['onderzoekid'],
            $research['onderzoeknaam'] ?? 'N.t.b.'
        );
    }

    /**
     * @param array{'documentid': float, 'documentdatum': string, 'documentnaam': string} $research
     */
    public function addDocument(array $research): self
    {
        $document = new Document(
            $research['documentid'],
            $research['documentdatum'],
            $research['documentnaam']
        );

        $this->research->setDocument($document);

        return $this;
    }

    public function build(): Research
    {
        return $this->research;
    }
}
