<?php

namespace organization\services;

use Craft;
use organization\models\Chart;
use organization\models\File;
use craft\helpers\StringHelper;

class ChartService
{
    public function download(Chart $chart): void
    {
        $file = $this->file($chart);

        $content = json_decode($file->getContents(), true);
        $tempPath = Craft::$app->path->tempAssetUploadsPath . '/' . $file->getName();

        /** @var resource $csvFile */
        $csvFile = fopen($tempPath, 'w');

        foreach ($content as $fields) {
            fputcsv($csvFile, $fields);
        }

        fclose($csvFile);

        if (file_exists($tempPath)) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($file->getName()) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($tempPath));

            readfile($tempPath);

            unlink($tempPath);
        }
    }

    private function file(Chart $chart): File
    {
        $content = array_map(fn ($chartData) => $chartData->toArray(), $chart->getChartData());

        $fileName = StringHelper::slugify($chart->getAbbreviation()) . '.csv';

        return new File(0, $fileName, (string) json_encode($content));
    }
}
