<?php

namespace organization\services;

use Craft;
use craft\elements\Asset;
use craft\elements\Entry;
use craft\errors\ElementNotFoundException;
use craft\errors\InvalidFieldException;
use craft\helpers\StringHelper;
use craft\models\EntryType;
use craft\models\Section;
use GuzzleHttp\Exception\GuzzleException;
use organization\actionHandlers\FetchDataActionHandler;
use organization\factories\OrganizationFactory;
use organization\models\File;
use organization\models\Organization;
use organization\repository\AssetRepository;
use organization\repository\OrganizationRepository;
use organization\repository\VolumeFolderRepository;
use organization\repository\wozit\WozItRepository;
use Throwable;
use yii\base\Exception;
use yii\base\InvalidConfigException;
use yii\di\NotInstantiableException;

class OrganizationService
{
    private const ENTRY_HANDLE = 'organization';

    private Section $section;
    private EntryType $entryType;
    protected WozItRepository $wozitRepository;
    protected OrganizationRepository $organizationRepository;
    protected VolumeFolderRepository $volumeFolderRepository;
    protected NationalAverageService $nationalAverageService;
    /**
     * @throws \Exception
     */
    public function __construct(
        WozItRepository        $wozitRepository,
        OrganizationRepository $organizationRepository,
        VolumeFolderRepository $volumeFolderRepository,
        NationalAverageService $nationalAverageService,
    )
    {
        $this->wozitRepository = $wozitRepository;
        $this->organizationRepository = $organizationRepository;
        $this->volumeFolderRepository = $volumeFolderRepository;
        $this->nationalAverageService = $nationalAverageService;
        $this->section = Craft::$app->sections->getSectionByHandle(self::ENTRY_HANDLE);

        $entryTypes = $this->section->getEntryTypes();

        if (!$entryTypes) {
            throw new \Exception('No entry types found!');
        }

        $this->entryType = reset($entryTypes);
    }

    /**
     * @throws Throwable
     * @throws NotInstantiableException
     * @throws ElementNotFoundException
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function save(Organization $organization): void
    {
        /** @phpstan-ignore-next-line */
        $entry = Entry::find()->externalId($organization->getIdentifier())->one();

        $fieldValues = [
            'associated' => $organization->getAssociated(),
            'associatedWith' => implode(',', $organization->getAssociatedWith()),
            'code' => $organization->getCode(),
            'externalId' => $organization->getIdentifier(),
            'organizationName' => $organization->getOrganizationName(),
            'organizationType' => $organization->getOrganizationType(),
            'rating' => $organization->getRating(),
            'website' => $organization->getWebsite(),
            'route' => $organization->getRoute(),
            'statusConsent' => $organization->getStatusConsent()
        ];

        $this->wozitRepository->getLogo($organization);

        if ($entry) {
            $entry->setFieldValues($fieldValues);
        } else {
            $entry = new Entry(array_merge([
                'authorId' => 1,
                'fieldLayoutId' => $this->entryType->fieldLayoutId,
                'postDate' => new \DateTime(),
                'typeId' => $this->entryType->id,
                'sectionId' => $this->section->id,
            ], $fieldValues));
        }

        $entry->title = $organization->getName();
        $entry->slug = StringHelper::slugify($organization->getOrganizationName());

        $isSaved = Craft::$app->elements->saveElement($entry);

        if (false === $isSaved) {
            throw new \Exception("Failed to save organization: {$organization->getName()}");
        }
    }

    /**
     * @throws NotInstantiableException
     * @throws InvalidConfigException
     * @throws InvalidFieldException
     */
    public function fetchByEntry(Entry $entry): Organization
    {
        /** @var int $externalId */
        $externalId = $entry->getFieldValue('externalId');

        /** @var Organization|false $organization */
        $organization = Craft::$app->getCache()->get($externalId);

        if (false === $organization) {
            $organization = OrganizationFactory::buildFromEntry($entry);

            /** @var FetchDataActionHandler $actionHandler */
            $actionHandler = Craft::$container->get(FetchDataActionHandler::class);
            $actionHandler->handle($organization);
        }

        return $organization;
    }

    /**
     * @param array<int> $externalIdentifiers
     * @return array<Organization>
     * @throws InvalidConfigException
     * @throws InvalidFieldException
     * @throws NotInstantiableException
     */
    public function fetchAssociated(array $externalIdentifiers): array
    {
        /** @phpstan-ignore-next-line */
        $entries = Entry::find()->externalId($externalIdentifiers)->all();

        return array_map(fn($entry) => $this->fetchByEntry($entry), $entries);
    }

    /**
     * @throws NotInstantiableException
     * @throws InvalidConfigException
     * @throws InvalidFieldException
     */
    public function fetchAffiliated(int $externalId): ?Organization
    {
        /** @phpstan-ignore-next-line */
        $entry = Entry::find()->externalId($externalId)->one();

        if (!$entry) {
            return null;
        }

        return $this->fetchByEntry($entry);
    }

    /**
     * @throws Throwable
     * @throws ElementNotFoundException
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function addLogo(Organization $organization, File $file): void
    {
        /** @phpstan-ignore-next-line */
        $entry = Entry::find()->externalId($organization->getIdentifier())->one();

        /** @var Asset $asset */
        $asset = (new AssetRepository())->save($organization, $file);

        $entry->setFieldValues(['logo' => [$asset->id]]);

        $isSaved = Craft::$app->elements->saveElement($entry);

        if (false === $isSaved) {
            throw new \Exception("Failed to save the logo to the organization: {$organization->getName()}");
        }

        $organization->addLogo($asset->getUrl());
    }

    public function overrideFromCache(Organization $organization): void
    {
        if (Craft::$app->getCache()->exists((int) $organization->getIdentifier())) {
            /** @var Organization $cache */
            $cache = Craft::$app->getCache()->get((int) $organization->getIdentifier());
            $organization->override($cache);
        }
    }

    /**
     * Fetch organizations and handle cleanup.
     *
     * @return Organization[]
     * @throws \Exception
     * @throws GuzzleException
     */
    public function fetchOrganizationsAndCleanup(): array
    {
        $this->volumeFolderRepository->create(VolumeFolderRepository::LOGO_FOLDER_NAME);
        $this->nationalAverageService->cache();
        $organizations = $this->wozitRepository->getOrganizations();
        $this->organizationRepository->cleanUp($organizations);

        return $organizations;
    }
}
