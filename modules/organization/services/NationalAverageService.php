<?php

namespace organization\services;

use Craft;
use organization\models\NationalAverage;
use organization\repository\wozit\WozItRepository;

class NationalAverageService
{
    private const CACHE_DURATION = 11232000;
    private const CACHE_KEY = 'national_average_charts';

    public function __construct(private readonly WozItRepository $wozitRepository)
    {
    }

    public function cache(): void
    {
        /** @var NationalAverage $nationalAverage */
        $nationalAverage = $this->wozitRepository->getNationalAverage();

        Craft::$app->getCache()->set(self::CACHE_KEY, $nationalAverage, self::CACHE_DURATION);
    }

    public function fetch(): NationalAverage
    {
        /** @var NationalAverage $nationalAverage */
        $nationalAverage = Craft::$app->getCache()->get(self::CACHE_KEY);

        if (!$nationalAverage) {
            $nationalAverage = $this->wozitRepository->getNationalAverage();
            Craft::$app->getCache()->set(self::CACHE_KEY, $nationalAverage, self::CACHE_DURATION);
        }

        return $nationalAverage;
    }
}
