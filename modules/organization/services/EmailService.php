<?php

declare(strict_types=1);

namespace organization\services;

use Craft;
use craft\mail\Message;
use Exception;
use yii\base\InvalidConfigException;

class EmailService
{
    private static ?EmailService $instance = null;
    /**
     * @var array|string[]
     */
    private array $mailList = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];
    /**
     * @var array|string[]
     */
    private array $supportMailList = [
        '<EMAIL>',
    ];
    /**
     * @var array<array<string, mixed>>
     */
    private array $errors = [];

    private function __construct()
    {
    }

    public static function getInstance(): EmailService
    {
        if (self::$instance === null) {
            self::$instance = new EmailService();
        }
        return self::$instance;
    }

    /**
     * Collect error messages.
     *
     * @param string $errorMessage
     * @param mixed|null $exception
     */
    public function addErrorMessage(string $errorMessage, mixed $exception = null): void
    {
        $timestamp = new \DateTime('now', new \DateTimeZone('Europe/Amsterdam'));
        $formattedTimestamp = $timestamp->format('Y-m-d H:i:s');

        $errorDetails = [
            'timestamp' => $formattedTimestamp,
            'message' => $errorMessage,
        ];

        if ($exception !== null) {
            $errorDetails['exception'] = [
                'type' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        $this->errors[] = $errorDetails;
    }

    /**
     * Send all collected error messages in a single email.
     *
     * @throws InvalidConfigException
     * @throws Exception
     */
    public function sendErrorEmails(): void
    {
        if (empty($this->errors)) {
            return;
        }

        $mailer = Craft::$app->getMailer();
        $email = new Message();
        $email->setFrom('<EMAIL>');
        $email->setTo($this->mailList);
        $email->setCc($this->supportMailList);
        $email->setSubject('API Error Notification');

        // Prepare HTML email body
        $htmlBody = "<h2>API Error Notification</h2>";
        $htmlBody .= "<p>The following errors occurred:</p><ul>";

        $attachments = [];

        foreach ($this->errors as $index => $error) {
            $htmlBody .= "<li><strong>Error #" . ($index + 1) . "</strong><br/>";
            $htmlBody .= "Timestamp: " . htmlspecialchars($error['timestamp'], ENT_QUOTES, 'UTF-8') . "<br/>";
            $htmlBody .= "Message: " . htmlspecialchars($error['message'], ENT_QUOTES, 'UTF-8') . "<br/>";

            if (isset($error['exception'])) {
                $exception = $error['exception'];
                $htmlBody .= "Exception Details:<br/>";
                $htmlBody .= "- Type: " . htmlspecialchars($exception['type'] ?? 'N/A', ENT_QUOTES, 'UTF-8') . "<br/>";
                $htmlBody .= "- Message: " . htmlspecialchars($exception['message'] ?? 'N/A', ENT_QUOTES, 'UTF-8') . "<br/>";
                $htmlBody .= "- Code: " . htmlspecialchars($exception['code'] ?? 'N/A', ENT_QUOTES, 'UTF-8') . "<br/>";
                $htmlBody .= "- File: " . htmlspecialchars($exception['file'] ?? 'N/A', ENT_QUOTES, 'UTF-8') . " (Line " . htmlspecialchars($exception['line'] ?? 'N/A', ENT_QUOTES, 'UTF-8') . ")<br/>";

                // Write stack trace to a file
                $stackTrace = $exception['trace'] ?? 'No stack trace available.';
                $logDirectory = Craft::getAlias('@storage/logs');
                $logFile = $logDirectory . '/error_stack_' . $index . '_' . time() . '.log';

                file_put_contents($logFile, $stackTrace);
                $attachments[] = $logFile;
            }

            $htmlBody .= "</li>";
        }

        $htmlBody .= "</ul>";

        // Set the email body as HTML
        $email->setHtmlBody($htmlBody);

        // Attach stack trace log files
        foreach ($attachments as $filePath) {
            $email->attach($filePath);
        }

        try {
            if (!$mailer->send($email)) {
                throw new Exception('Failed to send email.');
            }
        } catch (Exception $e) {
            throw new Exception("Error in sending email: {$e->getMessage()}");
        } finally {
            // Clean up log files after sending
            foreach ($attachments as $filePath) {
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
        }

        // Clear the errors after sending
        $this->errors = [];
    }
}
