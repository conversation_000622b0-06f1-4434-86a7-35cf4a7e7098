<?php

namespace organization\services;

use Craft;
use organization\models\File;

class DocumentService
{
    public function download(File $file): void
    {
        $tempPath = Craft::$app->path->tempAssetUploadsPath . '/' . $file->getName();
        $contents = base64_decode($file->getContents());
        file_put_contents($tempPath, $contents);

        if (file_exists($tempPath)) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/pdf');
            header('Content-Disposition: inline; filename="' . basename($file->getName()) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($tempPath));
            readfile($tempPath);
        }
    }
}
