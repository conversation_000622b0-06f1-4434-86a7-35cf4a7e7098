<?php

namespace organization\services;

use Craft;
use organization\models\File;

class LogoService
{
    public function upload(File $file): string
    {
        $tempPath = Craft::$app->path->tempAssetUploadsPath . '/' . $file->getName();

        if (file_exists($tempPath)) {
            throw new \Exception('File already exists');
        }

        file_put_contents($tempPath, base64_decode($file->getContents()));

        return $tempPath;
    }
}
