<?php

namespace organization;

use Craft;
use organization\ioc\Container;
use yii\base\Module as BaseModule;
use craft\events\RegisterCpNavItemsEvent;
use craft\events\RegisterTemplateRootsEvent;
use craft\web\twig\variables\Cp;
use craft\web\View;
use yii\base\Event;

/**
 * organization module
 *
 * @method static Module getInstance()
 */
class OrganizationModule extends BaseModule
{
    public function init(): void
    {
        Craft::setAlias('@organization', __DIR__);

        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'organization\\console\\controllers';
        } else {
            $this->controllerNamespace = 'organization\\controllers';
        }

        (new Container())->inject();

        parent::init();

        Craft::$app->onInit(function () {
            $this->attachEventHandlers();
        });
    }

    private function attachEventHandlers(): void
    {
        Event::on(
            View::class,
            View::EVENT_REGISTER_CP_TEMPLATE_ROOTS,
            function (RegisterTemplateRootsEvent $e) {
                $e->roots[$this->id] = __DIR__ . '/templates';
            }
        );

        Event::on(
            Cp::class,
            Cp::EVENT_REGISTER_CP_NAV_ITEMS,
            function (RegisterCpNavItemsEvent $event) {
                $event->navItems[] = [
                    'url' => 'organization/cache-clear',
                    'label' => 'Gemeente cache',
                ];
            }
        );
    }
}
