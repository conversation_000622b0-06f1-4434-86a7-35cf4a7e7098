<?php

namespace organization\repository;

use craft\elements\Entry;
use organization\models\Organization;

class OrganizationRepository
{
    private const ENTRY_HANDLE = 'organization';

    /**
     * @return Entry[]
     */
    public function findAll(): iterable
    {
        return Entry::find()->section(self::ENTRY_HANDLE)->all();
    }

    /**
     * @param iterable<int> $identifiers
     * @return iterable<Entry>
     */
    public function findByIdentifiers(iterable $identifiers): iterable
    {
        $organizations = Entry::find()
            ->section(self::ENTRY_HANDLE)
            ->where(['field_externalId_ycmxeevx' => $identifiers])
            ->all();

        if (count($organizations) === 0) {
            throw new \Exception('No organizations found!');
        }

        return $organizations;
    }

    public function findBySlug(string $slug): Entry
    {
        /** @var Entry|null $entry */
        $entry = Entry::find()
            ->section(self::ENTRY_HANDLE)
            ->slug($slug)
            ->one();

        if (null === $entry) {
            throw new \Exception("No organization found for {$slug}");
        }

        return $entry;
    }

    /**
     * @param array<Organization> $organizations
     */
    public function cleanUp(array $organizations): void
    {
        $organizationIds = array_map(fn ($organization) => $organization->getIdentifier(), $organizations);

        $oldOrganizations = Entry::find()
            ->section(self::ENTRY_HANDLE)
            ->where(sprintf('field_externalId_ycmxeevx NOT IN (%s)', implode(',', $organizationIds)))
            ->all();

        if (count($oldOrganizations) === 0) {
            return;
        }

        foreach ($oldOrganizations as $oldOrganization) {
            \Craft::$app->elements->deleteElement($oldOrganization);
        }
    }
}
