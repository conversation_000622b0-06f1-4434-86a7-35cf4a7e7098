<?php

namespace organization\repository\wozit;

use Craft;
use craft\elements\Asset;
use craft\elements\Entry;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use organization\builders\ResearchBuilder;
use organization\enums\ApiEndpoints;
use organization\enums\FeatureKeys;
use organization\factories\OrganizationFactory;
use organization\helpers\ValueTypeHelper;
use organization\models\Chart;
use organization\models\ChartData;
use organization\models\File;
use organization\models\Information;
use organization\models\NationalAverage;
use organization\models\Organization;
use organization\models\Statistics;
use organization\services\DocumentService;
use organization\services\EmailService;
use organization\services\LogoService;
use organization\services\OrganizationService;
use yii\base\InvalidConfigException;
use yii\di\NotInstantiableException;

class WozItRepository
{
    private Client $client;

    private EmailService $emailService;

    public function __construct()
    {
        $this->client = Craft::createGuzzleClient([
            'base_uri' => getenv('WOZ_IT_API_URI'),
            'auth' => [
                getenv('WOZ_IT_USERNAME'),
                getenv('WOZ_IT_PASSWORD')
            ],
        ]);
        $this->emailService = EmailService::getInstance();
    }

    /**
     * @return array<Organization>
     * @throws Exception|GuzzleException
     */
    public function getOrganizations(): array
    {
        $organizationList = $this->generateRequest(ApiEndpoints::ORGANIZATIONS->value, ['json' => (object)null]);

        return array_map(
            fn($organization) => OrganizationFactory::build($organization),
            $organizationList['organisaties']
        );
    }

    /**
     * @throws Exception|GuzzleException
     */
    public function getOrganization(int $id): Organization
    {
        $organizations = $this->generateRequest(ApiEndpoints::ORGANIZATIONS->value, ['json' => (object)null]);
        $organizations = array_filter($organizations['organisaties'], fn($o) => (int)$o['id'] === $id);

        if (empty($organizations)) {
            throw new Exception(sprintf('Organization with id: %d not found!', $id));
        }

        return OrganizationFactory::build(array_shift($organizations));
    }

    /**
     * @throws Exception|GuzzleException
     */
    public function getPublicFeatures(Organization $organization): void
    {
        $featureList = $this->generateRequest(ApiEndpoints::FEATURES->value,
            ['json' => ['sleutel' => FeatureKeys::PUBLIC->value, 'id' => $organization->getIdentifier()]]
        );

        $skippableCode = 'GMNW';

        foreach ($featureList['kenmerken'] as $feature) {
            if ($feature['code'] === $skippableCode) {
                continue;
            }

            $information = new Information(
                $feature['kenmerk'],
                $feature['code'] ?? '',
                $feature['datum'] ?? '',
                ValueTypeHelper::value($feature),
                $feature['weergave'],
                $feature['periode'],
            );

            $organization->addInformation($information);
        }
    }

    /**
     * @throws Exception|GuzzleException
     */
    public function getChartFeatures(Organization $organization): void
    {
        $featureList = $this->generateRequest(
            ApiEndpoints::FEATURES->value,
            ['json' => ['sleutel' => FeatureKeys::CHART->value, 'id' => $organization->getIdentifier()]]
        );

        $chartCodes = ['GMW', 'GWWA'];

        foreach ($featureList['kenmerken'] as $feature) {
            if (in_array($feature['code'], $chartCodes)) {
                $chart = new Chart($feature['kenmerk'], $feature['code']);

                foreach ($feature['waarden'] as $chartItem) {
                    $chartData = new ChartData(
                        $chartItem['datum'],
                        $chartItem['waarde'],
                        $chartItem['label'],
                        $feature['weergave'] ?? ''
                    );
                    $chart->addData($chartData);
                }

                $organization->addChart($chart);
            }
        }
    }

    /**
     * @throws Exception|GuzzleException
     */
    public function getStatistics(Organization $organization): void
    {
        $featureList = $this->generateRequest(
            ApiEndpoints::FEATURES->value,
            ['json' => ['sleutel' => FeatureKeys::STATISTICS->value, 'id' => $organization->getIdentifier()]]
        );

        foreach ($featureList['kenmerken'] as $feature) {
            $chartData = array_map(
                fn($chartItem) => new ChartData(
                    $chartItem['datum'],
                    $chartItem['waarde'],
                    $chartItem['label'],
                    $feature['weergave'] ?? '',
                ),
                $feature['waarden'] ?? []
            );

            $statistics = new Statistics(
                $feature['kenmerk'],
                $feature['code'] ?? '',
                $feature['datum'] ?? '',
                $feature['waarde'] ?? 0.0,
                $feature['weergave'],
                $feature['periode'],
                $chartData,
            );

            $organization->addStatistics($statistics);
        }
    }

    /**
     * @throws Exception|GuzzleException
     */
    public function getResearches(Organization $organization): void
    {
        $researches = $this->generateRequest(
            ApiEndpoints::RESEARCHES->value, ['json' => ['id' => $organization->getIdentifier()]]
        );

        foreach ($researches['onderzoeken'] as $research) {
            $organization->addResearch((new ResearchBuilder($research))->addDocument($research)->build());
        }
    }

    /**
     * @throws NotInstantiableException
     * @throws InvalidConfigException|GuzzleException
     */
    public function downloadDocument(int $documentId): void
    {
        $document = $this->generateRequest(
            ApiEndpoints::DOCUMENT->value, ['json' => ['id' => $documentId]]
        );
        /** @var DocumentService $documentService */
        $documentService = Craft::$container->get(DocumentService::class);

        $file = new File(
            $documentId,
            $document['document']['filename'],
            $document['document']['data'],
        );

        $documentService->download($file);
    }

    /**
     * @throws Exception|GuzzleException
     */
    public function getNationalAverage(): NationalAverage
    {
        $nationalCode = 1;

        $featureList = $this->generateRequest(
            ApiEndpoints::FEATURES->value,
            ['json' => ['sleutel' => FeatureKeys::CHART->value, 'code' => $nationalCode]]
        );

        $nationalAverage = new NationalAverage();

        foreach ($featureList['kenmerken'] as $feature) {
            $chart = new Chart($feature['kenmerk'], $feature['code'] ?? '');

            foreach ($feature['waarden'] as $chartItem) {
                $chartData = new ChartData(
                    $chartItem['datum'],
                    $chartItem['waarde'],
                    $chartItem['label'],
                    $feature['weergave'] ?? ''
                );
                $chart->addData($chartData);
            }

            $nationalAverage->addChart($chart);
        }

        return $nationalAverage;
    }

    /**
     * @param Organization $organization
     * @throws GuzzleException
     * @throws InvalidConfigException
     * @throws NotInstantiableException
     * @throws \Throwable
     */
    public function getLogo(Organization $organization): void
    {
        Craft::info(
            sprintf('Starting getLogo for organization %d', $organization->getIdentifier()),
            __METHOD__
        );

        $logos = Asset::find()
            ->volume('Gemeente-logos')
            ->filename($organization->getIdentifier() . '.*')
            ->all();

        // Fetch logo from API
        $logo = $this->generateRequest(
            ApiEndpoints::LOGO->value, ['json' => ['id' => $organization->getIdentifier()]]
        );

        // Validate API response
        if (empty($logo['afbeelding']) || empty($logo['bestandsnaam'])) {
            throw new Exception('Logo image is missing or invalid in the API response.');
        }

        $logoHash = substr(md5($logo['afbeelding']), 0, 50);
        $organization->setLogoHash($logoHash);

        // Find associated entry in Craft CMS
        /** @phpstan-ignore-next-line */
        $entry = Entry::find()->externalId($organization->getIdentifier())->one();

        if (!$entry instanceof Entry) {
            throw new Exception(sprintf('No entry found for externalId: %d.', $organization->getIdentifier()));
        }

        // Save logo hash to entry
        $entry->setFieldValue('logohash', $logoHash);
        if (!Craft::$app->elements->saveElement($entry)) {
            throw new Exception('Failed to save logohash: ' . json_encode($entry->getErrors()));
        }


        // Check if logo already exists in the system
        foreach ($logos as $assetLogo) {
            if (
                $assetLogo instanceof Asset &&
                $assetLogo->logohash === $logoHash
            ) {
                // Matching logo found, no upload needed
                OrganizationFactory::addLogo($organization, [$assetLogo]);
                return;
            }
        }

        // Upload new logo if no match found
        $fileName = sprintf('%d.%s', $organization->getIdentifier(), pathinfo($logo['bestandsnaam'], PATHINFO_EXTENSION));
        $file = new File(0, $fileName, $logo['afbeelding']);

        /** @var LogoService $logoService */
        $logoService = Craft::$container->get(LogoService::class);
        $tempPath = $logoService->upload($file);

        $organization->addLogo($tempPath);

        /** @var OrganizationService $organizationService */
        $organizationService = Craft::$container->get(OrganizationService::class);
        $organizationService->addLogo($organization, $file);
    }

    /**
     * @param string $type
     * @param array<string, mixed> $options
     * @return array<string, mixed>
     * @throws Exception|GuzzleException
     */
    public function generateRequest(string $type, array $options): array
    {
        $body = [];
        try {
            $response = $this->client->post($type, $options);
            $contentType = $response->getHeaderLine('Content-Type');
            if (!str_contains($contentType, 'application/json')) {
                // Use instance method to collect error message
                $this->emailService->addErrorMessage(
                    sprintf('Response is not JSON for this request: %s.', $type),
                    $response->getHeaders()
                );
            }

            $body = json_decode($response->getBody()->getContents(), true);
            if (empty($body)) {
                $this->emailService->addErrorMessage(
                    sprintf('Response is empty for %s.', $type),
                    $body
                );
            }

        } catch (Exception $e) {
            $this->emailService->addErrorMessage(
                sprintf('Error in %s: %s', $type, $e->getMessage())
            );
        }

        return $body;
    }
}
