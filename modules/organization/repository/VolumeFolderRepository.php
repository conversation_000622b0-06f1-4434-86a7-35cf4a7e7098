<?php

namespace organization\repository;

use Craft;
use craft\models\Volume;
use craft\records\VolumeFolder;
use craft\services\Assets;

class VolumeFolderRepository
{
    public const IMAGE_VOLUME_ID = 1;
    public const LOGO_FOLDER_NAME = 'Gemeente-logos';

    public function create(string $folderName): void
    {
        /** @var Assets $assets */
        $assets = Craft::$app->getAssets();

        /** @var Volume $volume */
        $volume = Craft::$app->getVolumes()->getVolumeById(self::IMAGE_VOLUME_ID);

        /** @var \craft\models\VolumeFolder $folder */
        $folder = $assets->findFolder(['name' => self::LOGO_FOLDER_NAME]);

        if ($volume->directoryExists($folderName) && $folder) {
            return;
        }

        $volumeFolder = new VolumeFolder();
        $volumeFolder->__set('name', $folderName);
        $volumeFolder->__set('path', $folderName . '/');
        $volumeFolder->__set('parentId', self::IMAGE_VOLUME_ID);
        $volumeFolder->__set('volumeId', $volume->id);

        $isSaved = $volumeFolder->save();

        if (!$isSaved) {
            throw new \Exception('Failed to create new volume folder: ' . $folder);
        }

        $volume->createDirectory($folderName);
    }
}
