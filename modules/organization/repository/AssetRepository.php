<?php

namespace organization\repository;

use Craft;
use craft\elements\Asset;
use craft\errors\ElementNotFoundException;
use organization\models\File;
use organization\models\Organization;
use craft\services\Assets;
use yii\base\Exception;

class AssetRepository
{
    /**
     * @throws ElementNotFoundException
     * @throws Exception
     * @throws \Throwable
     */
    public function save(Organization $organization, File $file): Asset
    {
        /** @var Assets $assets */
        $assets = Craft::$app->getAssets();

        /** @var \craft\models\VolumeFolder $folder */
        $folder = $assets->findFolder(['name' => VolumeFolderRepository::LOGO_FOLDER_NAME]);

        $asset = new Asset();
        $asset->tempFilePath = $organization->getLogo();
        $asset->filename = $file->getName();
        $asset->folderId = $folder->id;
        $asset->newFolderId = $folder->id;
        $asset->volumeId = $folder->volumeId;
        $asset->avoidFilenameConflicts = true;
        $asset->setScenario(Asset::SCENARIO_CREATE);
        $asset->setFieldValue('logohash', $organization->getLogoHash());
        if (!Craft::$app->elements->saveElement($asset)) {
            throw new \Exception('Failed to save asset: ' . json_encode($asset->getErrors()));
        }
        return $asset;
    }
}
