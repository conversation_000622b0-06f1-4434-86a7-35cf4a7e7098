<?php

namespace organization\console\controllers;

use Craft;
use craft\console\Controller;
use craft\helpers\Console;
use organization\actionHandlers\FetchDataActionHandler;
use organization\models\Organization;
use organization\services\OrganizationService;
use organization\services\EmailService;
use yii\base\InvalidConfigException;
use yii\console\ExitCode;
use yii\di\NotInstantiableException;

class SyncController extends Controller
{
    public $defaultAction = 'sync';

    /**
     * @throws NotInstantiableException
     * @throws InvalidConfigException
     * @throws \Throwable
     */
    public function actionSync(): int
    {
        $this->stdout("Started the organization sync! \n", Console::FG_GREEN);

        $emailService = EmailService::getInstance();
        /** @var OrganizationService $organizationService */
        $organizationService = Craft::$container->get(OrganizationService::class);

        // Handle organization fetching and cleanup with error handling
        $organizations = $this->handleWithError(
            fn() => $organizationService->fetchOrganizationsAndCleanup(),
            $emailService,
            'Fetching and cleaning up organizations failed!'
        );

        if ($organizations === null) {
            return ExitCode::UNSPECIFIED_ERROR;
        }

        /** @var FetchDataActionHandler $actionHandler */
        $actionHandler = Craft::$container->get(FetchDataActionHandler::class);

        foreach ($organizations as $organization) {
            $this->stdout("Insert {$organization->getName()} into database \n", Console::FG_GREEN);

            // Handle organization saving with error handling
            $this->handleWithError(
                fn() => $organizationService->save($organization),
                $emailService,
                "Failed to save organization {$organization->getName()}"
            );

            $this->stdout("Starting the sequence handler for {$organization->getName()} ({$organization->getIdentifier()}) \n", Console::FG_GREEN);

            // Handle the sequence handler with error handling
            $this->handleWithError(
                fn() => $this->handleOrganizationSequence($organizationService, $actionHandler, $organization),
                $emailService,
                "Failed to handle sequence for {$organization->getName()}"
            );
        }

        $emailService->sendErrorEmails();

        return ExitCode::OK;
    }

    /**
     * Handles the try-catch logic for repetitive error handling.
     *
     * @param callable $callback The function to execute within try-catch
     * @param EmailService $emailService The email service to send errors
     * @param string $errorMessage Custom error message
     * @return mixed|null Returns the result of the callback or null if an error occurs
     */
    private function handleWithError(callable $callback, EmailService $emailService, string $errorMessage): mixed
    {
        try {
            return $callback();
        } catch (\Exception $e) {
            $this->stdout($e->getMessage() . "\n", Console::FG_RED);
            $emailService->addErrorMessage($errorMessage . ": " . $e->getMessage());
            return null;
        }
    }

    /**
     * Handles the organization sequence with caching and action handling.
     *
     * @param OrganizationService $organizationService
     * @param FetchDataActionHandler $actionHandler
     * @param Organization $organization
     * @return void
     * @throws \Throwable
     */
    private function handleOrganizationSequence(OrganizationService $organizationService, FetchDataActionHandler $actionHandler, Organization $organization): void
    {
        $organizationService->overrideFromCache($organization);
        $actionHandler->handle($organization);
    }
}
