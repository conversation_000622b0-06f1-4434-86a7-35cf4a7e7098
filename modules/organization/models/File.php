<?php

namespace organization\models;

class File
{
    public function __construct(
        private readonly int $identifier,
        private readonly string $name,
        private readonly string $contents
    ) {
    }

    public function getIdentifier(): int
    {
        return $this->identifier;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getContents(): string
    {
        return $this->contents;
    }
}
