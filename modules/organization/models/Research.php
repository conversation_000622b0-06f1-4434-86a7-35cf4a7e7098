<?php

namespace organization\models;

use organization\models\Document;

class Research
{
    private ?Document $document;

    public function __construct(
        private readonly float $identifier,
        private readonly string $title
    ) {
    }

    public function getIdentifier(): float
    {
        return $this->identifier;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setDocument(Document $document): void
    {
        $this->document = $document;
    }

    public function getDocument(): ?Document
    {
        return $this->document;
    }
}
