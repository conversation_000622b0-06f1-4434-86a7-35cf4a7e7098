<?php

namespace organization\models;

class Document
{
    public function __construct(
        private readonly float $identifier,
        private readonly string $date,
        private readonly string $fileName
    ) {
    }

    public function getIdentifier(): float
    {
        return $this->identifier;
    }

    public function getFileName(): string
    {
        return $this->fileName;
    }

    public function getDate(): string
    {
        return $this->date;
    }
}
