<?php

namespace organization\models;

class Chart
{
    /** @var ChartData[] $data */
    private array $data = [];

    public function __construct(
        private readonly string $abbreviation,
        private readonly string $code
    ) {
    }

    public function getAbbreviation(): string
    {
        return $this->abbreviation;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function addData(ChartData $chartData): void
    {
        $this->data[] = $chartData;
    }

    /**
     * @return array<ChartData>
     */
    public function getChartData(): array
    {
        return $this->data;
    }

    /**
     * @return array<string>
     */
    public function getLabels(): array
    {
        if (empty($this->data)) {
            return [];
        }

        return array_map(fn ($x) => $x->getDate(), $this->data);
    }
    /**
     * @return array<float>
     */
    public function getValues(): array
    {
        if (empty($this->data)) {
            return [];
        }

        return array_map(fn ($x) => $x->getValue(), $this->data);
    }
}
