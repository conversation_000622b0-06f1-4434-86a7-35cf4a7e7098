<?php

namespace organization\models;

use JsonSerializable;

class ChartData implements JsonSerializable
{
    public function __construct(
        private readonly string $date,
        private readonly float $value,
        private readonly string $label,
        private readonly string $presentation
    ) {
    }

    public function getDate(): string
    {
        return $this->date;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function getPresentation(): string
    {
        return $this->presentation;
    }

    /**
     * @return array<mixed>
     */
    public function toArray(): array
    {
        return [$this->getValue(), $this->getLabel(), $this->getDate(), $this->getPresentation()];
    }

    /**
     * @return array<string, string|float>
     */
    public function jsonSerialize(): array
    {
        return $this->__serialize();
    }

    /**
     * @return array<string, string|float>
     */
    public function __serialize(): array
    {
        return [
            'date' => $this->date,
            'label' => $this->label,
            'value' => round($this->value, 1),
            'presentation' => $this->presentation
        ];
    }

    /**
     * @param array<string, mixed> $data
     */
    public function __unserialize(array $data): void
    {
        $this->date = $data['date'];
        $this->label = $data['label'];
        $this->value = $data['value'];
        $this->presentation = $data['presentation'];
    }
}
