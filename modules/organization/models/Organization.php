<?php

namespace organization\models;

use Doctrine\Common\Collections\ArrayCollection;
use organization\enums\OrganizationType;
use organization\models\Research;

class Organization
{
    /** @var Research[] $researches */
    private array $researches = [];

    /** @var Information[] $information */
    private array $information = [];

    /** @var ArrayCollection<string, Chart> $charts */
    private ArrayCollection $charts;

    /** @var Statistics[] $statistics */
    private array $statistics = [];

    /**
     * @param array<int> $associatedWith
     */
    public function __construct(
        private readonly float   $identifier,
        private readonly string  $name,
        private readonly string  $organizationName,
        private readonly string  $code,
        private readonly string  $organizationType,
        private readonly string  $route,
        private readonly ?float  $rating,
        private readonly ?string $website,
        private readonly ?array  $associatedWith,
        private readonly ?int    $associated,
        private readonly ?string $statusConsent,
        private ?string          $logo,
        private ?string          $logoHash
    )
    {
    }

    public function getIdentifier(): float
    {
        return $this->identifier;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getOrganizationName(): string
    {
        return $this->organizationName;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getRating(): ?float
    {
        return $this->rating;
    }

    public function getOrganizationType(): string
    {
        return $this->organizationType;
    }

    public function getWebsite(): ?string
    {
        return $this->website;
    }

    /**
     * @return array<int>
     */
    public function getAssociatedWith(): ?array
    {
        return $this->associatedWith;
    }

    public function getAssociated(): ?int
    {
        return $this->associated;
    }

    public function isAssociation(): bool
    {
        return $this->getOrganizationType() === OrganizationType::ASSOCIATION->value;
    }

    public function getRoute(): string
    {
        return $this->route;
    }

    public function addResearch(Research $research): void
    {
        array_push($this->researches, $research);
    }

    /**
     * @return array<Research>
     */
    public function getResearches(): array
    {
        return $this->researches;
    }

    public function resetResearches(): void
    {
        $this->researches = [];
    }

    public function addInformation(Information $information): void
    {
        array_push($this->information, $information);
    }

    /**
     * @return array<Information>
     */
    public function getInformation(): array
    {
        return $this->information;
    }

    public function resetInformation(): void
    {
        $this->information = [];
    }

    public function addChart(Chart $chart): void
    {
        if (empty($this->charts)) {
            $this->charts = new ArrayCollection();
        }

        $this->charts->set($chart->getCode(), $chart);
    }

    /**
     * @return ArrayCollection<string, Chart>
     */
    public function getCharts(): ArrayCollection
    {
        if (empty($this->charts)) {
            $this->charts = new ArrayCollection();
        }

        return $this->charts;
    }

    public function resetCharts(): void
    {
        $this->charts = new ArrayCollection();
    }

    /**
     * @return array<Statistics>
     */
    public function getStatistics(): array
    {
        return $this->statistics;
    }

    public function resetStatistics(): void
    {
        $this->statistics = [];
    }

    public function addStatistics(Statistics $statistics): void
    {
        $this->statistics[] = $statistics;
    }

    public function getStatusConsent(): ?string
    {
        return $this->statusConsent;
    }

    public function addLogo(string $logoPath): void
    {
        $this->logo = $logoPath;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function getTitle(): string
    {
        if ($this->isAssociation()) {
            return $this->getOrganizationName();
        }

        return sprintf('%s %s', strtolower($this->getOrganizationType()), $this->getOrganizationName());
    }

    public function override(Organization $organization): void
    {
        $this->charts = $organization->getCharts();
        $this->information = $organization->getInformation();
        $this->researches = $organization->getResearches();
        $this->statistics = $organization->getStatistics();
    }

    public function getLogoHash(): ?string
    {
        return $this->logoHash ?? null;
    }

    public function setLogoHash(?string $hash): void
    {
        $this->logoHash = $hash;
    }
}
