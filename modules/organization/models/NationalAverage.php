<?php

namespace organization\models;

use Doctrine\Common\Collections\ArrayCollection;

class NationalAverage
{
    /** @var ArrayCollection<string, Chart> $charts */
    private ArrayCollection $charts;

    public function __construct()
    {
        $this->charts = new ArrayCollection();
    }

    public function addChart(Chart $chart): void
    {
        $this->charts->set($chart->getCode(), $chart);
    }

    /**
     * @return ArrayCollection<string, Chart> $charts
     */
    public function getCharts(): ArrayCollection
    {
        return $this->charts;
    }
}
