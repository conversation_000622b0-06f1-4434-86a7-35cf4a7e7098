<?php

namespace organization\models;

class Information
{
    public function __construct(
        private readonly string $title,
        private readonly string $code,
        private readonly string $date,
        private readonly float | string $value,
        private readonly string $presentation,
        private readonly ?string $period,
    ) {
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getDate(): string
    {
        return $this->date;
    }

    public function getPeriod(): ?string
    {
        return $this->period;
    }

    public function getValue(): float | string
    {
        return $this->value;
    }

    public function getPresentation(): string
    {
        return $this->presentation;
    }
}
