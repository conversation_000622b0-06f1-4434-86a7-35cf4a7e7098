<?php

namespace organization\models;

class Statistics extends Information
{
    /**
     * @param ChartData[] $history
     */
    public function __construct(
        string $title,
        string $code,
        string $date,
        float $value,
        string $presentation,
        ?string $period,
        private ?array $history,
    ) {
        parent::__construct($title, $code, $date, $value, $presentation, $period);

        $this->sortHistory();
    }

    /**
     * @return ChartData[]
     */
    public function getHistory(): ?array
    {
        return array_splice($this->history, 1);
    }

    public function getCurrentYear(): ?ChartData
    {
        if (!empty($this->history)) {
            return reset($this->history);
        }

        return null;
    }

    public function sortHistory(): void
    {
        usort(
            $this->history,
            /** @phpstan-ignore-next-line */
            fn (ChartData $a, ChartData $b) => (new \DateTime($a->getDate()))->getTimestamp() < (new \DateTime($b->getDate()))->getTimestamp()
        );
    }
}
