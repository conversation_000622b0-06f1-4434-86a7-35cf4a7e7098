<?php

declare(strict_types=1);

namespace organization\helpers;

use organization\enums\OrganizationType;

class OrganizationNameHelper
{
    /**
     * @param array{'naam': string, 'organisatie-type': string} $organization
     */
    public static function convert(array $organization): string
    {
        if ($organization['organisatie-type'] === OrganizationType::ORGANIZATION->value) {
            return trim(str_replace('Gemeente', '', $organization['naam']));
        }

        return $organization['naam'];
    }
}
