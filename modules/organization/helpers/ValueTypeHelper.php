<?php

declare(strict_types=1);

namespace organization\helpers;

final class ValueTypeHelper
{
    /**
     * @param array{'type': string, 'tekstwaarde': string, 'waarde': float} $values
     */
    public static function value(array $values): float | string
    {
        return match ($values['type']) {
            'Datum' => $values['tekstwaarde'] ?? '',
            default => $values['waarde'] ?? 0.0,
        };
    }
}
