<?php

namespace modules\twigextender;

use Craft;
use modules\twigextender\vimeo\VimeoExtension;
use yii\base\Module as BaseModule;
use Twig\Extra\String\StringExtension;
use Twig\Extra\Intl\IntlExtension;

/**
 * twig-extender module
 *
 * @method static Module getInstance()
 */
class TwigExtenderModule extends BaseModule
{
    public function init(): void
    {
        Craft::setAlias('@modules/twigextender', __DIR__);

        // Set the controllerNamespace based on whether this is a console or web request
        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'modules\\twigextender\\console\\controllers';
        } else {
            $this->controllerNamespace = 'modules\\twigextender\\controllers';
        }

        parent::init();

        if (Craft::$app->getRequest()->getIsSiteRequest()) {
            Craft::$app->view->registerTwigExtension(new IntlExtension());
            Craft::$app->view->registerTwigExtension(new StringExtension());
            Craft::$app->view->registerTwigExtension(new VimeoExtension());
        }
    }
}
