<?php

declare(strict_types=1);

namespace modules\twigextender\vimeo;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

final class VimeoExtension extends AbstractExtension
{
    public function getFilters()
    {
        return [
            new TwigFilter('disable_tracking_cookies', [$this, 'disableTrackingCookies']),
        ];
    }

    /**
     * Usage: {{ vimeoUrl | disable_tracking_cookies }}
     */
    public function disableTrackingCookies(string $videoUrl): string
    {
        $parsedUrl = parse_url($videoUrl);

        if (empty($parsedUrl)) {
            return $videoUrl;
        }

        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $query);

            if (isset($query['dnt'])) {
                $this->enableDnt($query);
                $videoUrl = $this->buildUrl($parsedUrl, $query);
            } else {
                $query['dnt'] = 1;
            }

            $videoUrl = $this->buildUrl($parsedUrl, $query);
        } else {
            $videoUrl = $this->buildUrl($parsedUrl, ['dnt' => 1]);
        }

        return $videoUrl;
    }

    /**
     * @param array<string, int> $query
     */
    private function enableDnt(array &$query): void
    {
        if ($query['dnt'] == 0) {
            $query['dnt'] = 1;
        }
    }

    /**
     * @param array<string, string|int> $parsedUrl
     * @param array<string, string|int> $query
     */
    private function buildUrl(array $parsedUrl, array $query): string
    {
        return $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . '?' . http_build_query($query);
    }
}
