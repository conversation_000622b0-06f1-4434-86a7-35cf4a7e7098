<?php

namespace search;

use Craft;
use yii\base\Module as BaseModule;
use craft\elements\Entry;

/**
 * search module
 *
 * @method static Module getInstance()
 */
class SearchModule extends BaseModule
{
    public function init(): void
    {
        Craft::setAlias('@search', __DIR__);

        // Set the controllerNamespace based on whether this is a console or web request
        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'search\\console\\controllers';
        } else {
            $this->controllerNamespace = 'search\\controllers';
        }

        parent::init();

        // Defer most setup tasks until Craft is fully initialized
        Craft::$app->onInit(function() {
            $this->attachEventHandlers();
            // ...
        });
    }

    private function attachEventHandlers(): void
    {
        // Register event handlers here ...
        // (see https://craftcms.com/docs/4.x/extend/events.html to get started)
    }
}
