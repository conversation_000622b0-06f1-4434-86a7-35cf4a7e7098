<?php

namespace search\controllers;

use Craft;
use craft\web\Controller;
use craft\web\View;
use craft\helpers\App;

/**
 * Index controller
 */
class IndexController extends Controller
{
    public $defaultAction = 'index';
    protected array|int|bool $allowAnonymous = self::ALLOW_ANONYMOUS_LIVE;

    /**
     * search/index action
     */
    public function actionIndex(string $query = ''): string
    {
        if (!$query) {
            $query = $this->request->getBodyParam('query') ?? '';
        }

        $lang = Craft::$app->language;
        $siteHandle = Craft::$app->sites->getCurrentSite()->handle;
        $env = App::env('MEILISEARCH_INDEX_PREFIX');

        return Craft::$app->getView()->renderTemplate('search/index', [
                'lang' =>  $lang,
                'searchIndex' =>  "{$env}_{$lang}_{$siteHandle}_global",
                'host' => App::env('MEILISEARCH_CLIENT_HOST'),
                'api_key' => App::env('MEILISEARCH_CLIENT_KEY'),
                'query' =>  $query,
                'filters' => [],
                'results' => [],
                'total' => 0,
            ],
            View::TEMPLATE_MODE_SITE
        );
    }
}
