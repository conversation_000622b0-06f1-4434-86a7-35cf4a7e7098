<?php

namespace modelupload\services;

abstract class DataProcessor
{
    public string $fileName;
    public string $endpoint;

    /**
     * Returns the name of the organization field, depending on the subclass.
     *
     * @return string
     */
    abstract public function getOrganisatieKey(): string;

    /**
     * @param array<string, mixed> $json
     * @return array<int, array<string, mixed>>
     */
    public function parseCsvColumns(array $json): array
    {
        $result = [];

        foreach ($json as $key => $values) {
            if (is_array($values)) {
                foreach ($values as $data) {
                    if (is_array($data)) {
                        $generalFields = array_filter($data, fn($fieldValue) => !is_array($fieldValue));

                        foreach ($data as $nestedData) {
                            if (is_array($nestedData)) {
                                foreach ($nestedData as $rowData) {
                                    if (is_array($rowData)) {
                                        $row = $rowData;
                                        foreach ($generalFields as $generalKey => $generalValue) {
                                            if (!array_key_exists($generalKey, $row)) {
                                                $row[$generalKey] = $generalValue;
                                            }
                                        }
                                        $result[] = $row;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $result;
    }
}
