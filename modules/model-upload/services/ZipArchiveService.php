<?php

declare(strict_types=1);

namespace modelupload\services;

class ZipArchiveService
{
    public static function unzip(string $source, string $target): void
    {
        $zip = new \ZipArchive();

        if ($zip->open($source) === true) {
            $zip->extractTo($target);
            $zip->close();
        } else {
            throw new \Exception('Failed to open the zip file for extraction.');
        }
    }
}
