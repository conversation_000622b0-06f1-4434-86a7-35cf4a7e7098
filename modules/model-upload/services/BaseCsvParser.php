<?php

 namespace modelupload\services;

abstract class BaseCsvParser
{
    /**
     * @return string
     */
    abstract public function getDataKey(): string;

    /**
     * @param array<string, mixed> $json
     * @return array<int, array<string, mixed>>
     */
    public function parseCsvColumns(array $json): array
    {
        $result = [];
        foreach ($json as $key => $values) {
            foreach ($values as $data) {
                $dataKey = $this->getDataKey();
                $nestedData = isset($data[$dataKey]) && is_array($data[$dataKey]) ? $data[$dataKey] : [null];
                foreach ($nestedData as $yearData) {
                    $row = [];
                    $keys = array_keys($data);
                    foreach ($keys as $originalKey) {
                        if (isset($data[$originalKey])) {
                            $row[$originalKey] = $data[$originalKey];
                        } elseif (isset($yearData[$originalKey])) {
                            $row[$originalKey] = $yearData[$originalKey];
                        } else {
                            $row[$originalKey] = null;
                        }
                    }
                    $result[] = $row;
                }
            }
        }
        return $result;
    }
}
