<?php

namespace modelupload\services;

use AllowDynamicProperties;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

#[AllowDynamicProperties] class APIClient
{
    private Client $client;

    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * @param string $endpoint
     *
     * @return array<int, array<string, mixed>>|null
     *
     * @throws GuzzleException
     * @throws Exception
     */
    public function fetchData(string $endpoint): ?array
    {
        try {
            $url = getenv('WOZ_BENCHMARK_DASHBOARD_API_URI') . $endpoint;
            $response = $this->client->get($url, [
                'headers' => [
                    'Accept' => '*/*',
                    'Content-Type' => 'application/json',
                    'Authorization' => getenv('WOZ_BENCHMARK_BEARER_TOKEN')
                ],
            ]);

            $contentType = $response->getHeaderLine('Content-Type');
            if (!str_contains($contentType, 'text/plain')) {
                throw new \Exception("Exception occurred while fetching data from API for content type: $contentType for $endpoint");
            }
            $body = json_decode($response->getBody()->getContents(), true);
            if (empty($body)) {
                throw new \Exception("exception occurred while fetching data from API for content is empty for $endpoint");
            }

            return $body;
        } catch (Exception $e) {
            throw new \Exception("Exception occurred while fetching data from API for $endpoint: " . $e->getMessage());
        }
    }
}
