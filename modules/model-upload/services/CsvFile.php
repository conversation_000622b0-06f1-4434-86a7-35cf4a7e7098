<?php

namespace modelupload\services;

use craft\elements\Asset;
use craft\helpers\Assets;
use craft\models\VolumeFolder;
use Exception;
use Craft;

class CsvFile
{
    private const VOLUME = 'documents';
    private const SUB_FOLDER = '02.-Over-ons/06.-De-WOZ-in-cijfers/Data/';

    /**
     * @param array<int, array<string, mixed>> $data
     * @param string $csvFilePath
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function generateCsvInTempFolder(array $data, string $csvFilePath): bool
    {
        try {
            $file = fopen($csvFilePath, 'w');
            if ($file === false) {
                throw new Exception("Failed to open file for writing: " . $csvFilePath);
            }

            $headers = $this->extractHeaders($data);
            fputcsv($file, $headers);

            foreach ($data as $index => $row) {
                $flattenedRow = $this->flattenArray($row);
                $rows = $this->rearrangeRow($flattenedRow, $headers);
                foreach ($rows as $rowValues) {
                    if (!empty($rowValues)) {
                        fputcsv($file, $rowValues);
                    }
                }
            }

            return fclose($file);
        } catch (Exception $e) {
            throw new \Exception("Failed to convert JSON to CSV: " . $e->getMessage());
        }
    }

    /**
     * @param array<int, array<string, mixed>> $data
     *
     * @return array<int, string>
     */
    private function extractHeaders(array $data): array
    {
        $headers = [];
        foreach ($data as $item) {
            $flattened = $this->flattenArray($item);
            $headers = array_merge($headers, array_map(function ($key) {
                return preg_replace('/^\d+\./', '', $key);
            }, array_keys($flattened)));
        }

        return array_unique($headers);
    }

    /**
     * @param array<string, mixed> $array
     * @param string $prefix
     *
     * @return array<string, mixed>
     */
    private function flattenArray(array $array, string $prefix = ''): array
    {
        $result = [];
        foreach ($array as $key => $value) {
            $new_key = $prefix === '' ? $key : $prefix . '.' . $key;
            if (is_array($value)) {
                $result = array_merge($result, $this->flattenArray($value, $new_key));
            } else {
                $result[$new_key] = $value;
            }
        }

        return $result;
    }

    /**
     * @param array<string, mixed> $flattenedRow
     * @param array<int, string> $headers
     *
     * @return array<int, array<int, mixed>>
     */
    private function rearrangeRow(array $flattenedRow, array &$headers): array
    {
        $rows = [];
        $headerCount = count($headers);
        foreach ($headers as $header) {
            $matchedValues = array_values(array_filter($flattenedRow, function ($key) use ($header) {
                return preg_replace('/^\d+\./', '', $key) === $header;
            }, ARRAY_FILTER_USE_KEY));

            for ($i = 0, $n = count($matchedValues); $i < $n; $i++) {
                if (!isset($rows[$i])) {
                    $rows[$i] = array_fill(0, $headerCount, '');
                }
                $rows[$i][array_search($header, $headers)] = $matchedValues[$i];
            }
        }

        return $rows;
    }

    public function saveIntoAssets(string $csvFilePath, string $fileName): bool
    {
        try {
            $volume = Craft::$app->volumes->getVolumeByHandle(self::VOLUME);
            if (!$volume) {
                throw new \Exception(sprintf('Volume with handle "%s" not found.', self::VOLUME));
            }

            // Find or create the subfolder
            $subFolder = Craft::$app->assets->findFolder([
                'volumeId' => $volume->id,
                'path' => self::SUB_FOLDER,
            ]);

            if (!$subFolder) {
                $subFolder = new VolumeFolder();
                $subFolder->volumeId = $volume->id;
                $subFolder->name = self::SUB_FOLDER;
                $subFolder->path = self::SUB_FOLDER;
                Craft::$app->assets->createFolder($subFolder);
            }

            $folderId = $subFolder->id;
            $fileContents = file_get_contents($csvFilePath);
            if ($fileContents === false) {
                throw new \Exception("Failed to read contents of file at '{$csvFilePath}'.");
            }
            $existingAsset = Asset::find()
                ->filename($fileName)
                ->folderId($folderId)
                ->one();

            if ($existingAsset) {
                $tempPath = Assets::tempFilePath($fileName);
                file_put_contents($tempPath, $fileContents);

                /** @var Asset $existingAsset */
                $existingAsset->setScenario(Asset::SCENARIO_REPLACE);
                $existingAsset->tempFilePath = $tempPath;
                $existingAsset->setFieldValue('publicationDate', new \DateTime());

                if (!Craft::$app->elements->saveElement($existingAsset)) {
                    throw new \Exception(sprintf('Failed to update asset for file "%s" in volume "%s".', $fileName, self::VOLUME));
                }
            } else {
                $tempPath = Assets::tempFilePath($fileName);
                file_put_contents($tempPath, $fileContents);

                $asset = new Asset();
                $asset->tempFilePath = $tempPath;
                $asset->filename = $fileName;
                $asset->newFolderId = $folderId;
                $asset->volumeId = $volume->id;
                $asset->avoidFilenameConflicts = true;
                $asset->setScenario(Asset::SCENARIO_CREATE);
                $asset->setFieldValue('publicationDate', new \DateTime());

                if (!Craft::$app->elements->saveElement($asset)) {
                    throw new \Exception(sprintf('Failed to save asset for file "%s" to volume "%s".', $fileName, self::VOLUME));
                }
            }

            return true;
        } catch (Exception $e) {
            throw new \Exception(sprintf('Failed to save CSV to file system: %s', $e->getMessage()));
        }
    }
}
