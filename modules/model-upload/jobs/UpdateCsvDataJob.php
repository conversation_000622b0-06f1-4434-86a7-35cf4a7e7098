<?php

namespace modelupload\jobs;

use Craft;
use GuzzleHttp\Exception\GuzzleException;
use modelupload\services\APIClient;
use modelupload\services\Benchmark;
use modelupload\services\CsvFile;
use modelupload\services\ObjectInventory;
use modelupload\services\OrganizationCharacteristics;
use modelupload\services\TimeSeries;
use organization\services\EmailService;
use Throwable;

class UpdateCsvDataJob
{
    private EmailService $emailService;

    public function __construct(
        private APIClient $apiClient = new APIClient(),
        private CsvFile   $csvFile = new CsvFile()
    )
    {
        $this->emailService = EmailService::getInstance();
    }

    /**
     * @throws Throwable
     * @throws GuzzleException
     */
    public function execute(): void
    {
        $this->generateCsvFile(new Benchmark());
        $this->generateCsvFile(new TimeSeries());
        $this->generateCsvFile(new ObjectInventory());
        $this->generateCsvFile(new OrganizationCharacteristics());
    }

    /**
     * @param Benchmark|TimeSeries|ObjectInventory|OrganizationCharacteristics $type
     * @throws Throwable
     */
    public function generateCsvFile(Benchmark|TimeSeries|ObjectInventory|OrganizationCharacteristics $type): void
    {
        try {
            echo (sprintf("Processing endpoint %s", $type->endpoint)) . PHP_EOL;

            $parsedData = $this->apiClient->fetchData($type->endpoint);
            echo sprintf("Fetched data for endpoint: %s%s", $type->endpoint, PHP_EOL);

            /** @phpstan-ignore-next-line */
            $result = $type->parseCsvColumns($parsedData);
            echo sprintf("Parsed CSV columns for endpoint: %s%s", $type->endpoint, PHP_EOL);

            $csvFilePath = sprintf('%s/%s', Craft::$app->getPath()->getTempPath(), $type->fileName);
            if ($this->csvFile->generateCsvInTempFolder($result, $csvFilePath)) {
                echo sprintf("Generated CSV in temp folder: %s%s", $csvFilePath, PHP_EOL);
                $this->csvFile->saveIntoAssets($csvFilePath, $type->fileName);
                echo sprintf("Saved CSV into assets: %s%s", $csvFilePath, PHP_EOL);
            } else {
                echo sprintf("Failed to generate CSV in temp folder: %s%s", $csvFilePath, PHP_EOL);
            }

            echo (sprintf("Finished endpoint %s \n", $type->endpoint)) . PHP_EOL;

        } catch (Throwable $e) {
            echo sprintf("Error processing endpoint %s: %s%s", $type->endpoint, $e->getMessage(), PHP_EOL);
            $this->emailService->addErrorMessage(sprintf("Error processing endpoint %s: %s \n", $type->endpoint, $e->getMessage()));
            $this->emailService->sendErrorEmails();
        }
    }

    protected function defaultDescription(): string
    {
        return Craft::t('app', 'Updating CSV data from API.');
    }
}