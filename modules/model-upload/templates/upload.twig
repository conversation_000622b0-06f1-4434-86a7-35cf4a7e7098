{% extends "_layouts/cp.twig" %}
{% import '_includes/forms.twig' as forms %}

{% set title = "Upload IMWOZ" %}

{% set fullPageForm = true %}

{% set mainFormAttributes = {
    enctype: "multipart/form-data"
} %}

{% block content %}
	{{ actionInput('model-upload/upload') }}

    {% if model is defined and model.getErrors('zipFile') %}
        <div class="cp-message error">
            <p>Validation errors occurred during file upload:</p>
            <ul>
                {% for error in model.getErrors('zipFile') %}
                    <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}

    <p>Je kunt hier het IMWOZ model uploaden. Probeer waar mogelijk dezelfde folderstructuur aan te houden als voorheen.</p> <p>Wanneer deze toch veranderd moet mogelijk de asset index <a href="{{ cpUrl('utilities/asset-indexes') }}">opnieuw geindexeerd worden.</a></p>

    {{ forms.file({
        label: "Upload new IMWOZ",
        name: "zipFile",
        id: "zipFile",
        required: true,
    }) }}

{% endblock %}
