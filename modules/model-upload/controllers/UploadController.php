<?php

declare(strict_types=1);

namespace modelupload\controllers;

use Craft;
use craft\web\Controller;
use craft\web\UploadedFile;
use modelupload\models\UploadModel;
use modelupload\services\ZipArchiveService;
use yii\web\Response;

class UploadController extends Controller
{
    public $defaultAction = 'index';
    protected array|int|bool $allowAnonymous = self::ALLOW_ANONYMOUS_NEVER;
    private string $volume = 'documents';

    public function actionIndex(): Response
    {
        $model = new UploadModel();

        if (Craft::$app->request->isPost) {
            $model->zipFile = UploadedFile::getInstanceByName('zipFile');

            if ($model->validate()) {
                $tempFilePath = Craft::$app->getPath()->getTempAssetUploadsPath() . '/' . $model->zipFile->name;
                $model->zipFile->saveAs($tempFilePath);

                $targetDirectory = 'imwoz-models/model';

                /** @var craft\fs\Local $fs */
                $fs = Craft::$app->getFs()->getFilesystemByHandle($this->volume);
                /** @phpstan-ignore-next-line */
                $fs->createDirectory($targetDirectory);

                ZipArchiveService::unzip($tempFilePath,
                    /** @phpstan-ignore-next-line */
                    $fs->getRootPath() . '/' . $targetDirectory
                );

                return $this->renderTemplate('model-upload/upload-success');
            }
        }

        return $this->renderTemplate('model-upload/upload', ['model' => $model]);
    }
}
