<?php

declare(strict_types=1);

namespace modelupload;

use Craft;
use yii\base\Module as BaseModule;
use craft\events\RegisterCpNavItemsEvent;
use craft\events\RegisterTemplateRootsEvent;
use craft\web\twig\variables\Cp;
use craft\web\View;
use yii\base\Event;

/**
 * @method static Module getInstance()
 */
class ModelUploadModule extends BaseModule
{
    public function init(): void
    {
        Craft::setAlias('@modelupload', __DIR__);

        // Set the controllerNamespace based on whether this is a console or web request
        if (Craft::$app->request->isConsoleRequest) {
            $this->controllerNamespace = 'modelupload\\console\\controllers';
        } else {
            $this->controllerNamespace = 'modelupload\\controllers';
        }

        parent::init();

        // Defer most setup tasks until Craft is fully initialized
        Craft::$app->onInit(function() {
            $this->attachEventHandlers();
        });
    }

    private function attachEventHandlers(): void
    {
        Event::on(
            View::class,
            View::EVENT_REGISTER_CP_TEMPLATE_ROOTS,
            function (RegisterTemplateRootsEvent $e) {
                $e->roots[$this->id] = __DIR__ . '/templates';
            }
        );

        Event::on(
            Cp::class,
            Cp::EVENT_REGISTER_CP_NAV_ITEMS,
            function(RegisterCpNavItemsEvent $event) {
                $event->navItems[] = [
                    'url' => 'model-upload/upload',
                    'label' => 'Upload IMWOZ Model',
                ];
            }
        );

    }
}
