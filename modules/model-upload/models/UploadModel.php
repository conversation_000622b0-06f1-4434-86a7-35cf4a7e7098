<?php

declare(strict_types=1);

namespace modelupload\models;

use craft\base\Model;

class UploadModel extends Model
{
    public ?\yii\web\UploadedFile $zipFile = null;

    /**
     * @return array<int|string, array<int|string, mixed>>
     */
    public function rules(): array
    {
        return [
            [['zipFile'], 'file', 'extensions' => ['zip']],
            [['zipFile'], 'file', 'maxSize' => 1024 * 1024 * 10], // 10 MB
        ];
    }
}
