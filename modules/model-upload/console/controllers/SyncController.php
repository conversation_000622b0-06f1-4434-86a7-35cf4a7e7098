<?php

namespace modelupload\console\controllers;

use craft\console\Controller;
use craft\helpers\Console;
use GuzzleHttp\Exception\GuzzleException;
use modelupload\jobs\UpdateCsvDataJob;
use yii\console\ExitCode;
use Craft;

class SyncController extends Controller
{
    public $defaultAction = 'sync';

    /**
     * @throws GuzzleException
     * @throws \Throwable
     */
    public function actionSync(): int
    {
        $this->stdout("Running UpdateCsvDataJob... \n", Console::FG_GREEN);

        $job = new UpdateCsvDataJob();
        $job->execute();

        $this->stdout("UpdateCsvDataJob has run successfully \n", Console::FG_GREEN);
        return ExitCode::OK;
    }
}
