{"name": "@redkiwi/waarderingskamer", "version": "0.0.1", "dependencies": {"@alpinejs/focus": "^3.13.8", "@tailwindcss/container-queries": "^0.1.1", "alpinejs": "^3.13.0", "chart.js": "^4.4.0", "sass": "^1.67.0"}, "devDependencies": {"@savvywombat/tailwindcss-grid-areas": "^3.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.10", "@types/alpinejs": "^3.13.0", "autoprefixer": "^10.4.15", "postcss": "^8.4.30", "postcss-import": "^15.1", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.4", "tailwindcss": "^3.3.3", "typescript": "^5.2.2", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-manifest-sri": "^0.1", "vite-plugin-restart": "^0.3.1"}, "engines": {"node": ">=18"}, "scripts": {"build": "NODE_ENV=production vite build", "serve": "NODE_ENV=dev vite", "prettier": "prettier --write ."}}