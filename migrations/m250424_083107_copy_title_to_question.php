<?php

namespace craft\contentmigrations;

use Craft;
use craft\db\Migration;
use craft\elements\Entry;

/**
 * m250424_083107_copy_title_to_question migration.
 */
class m250424_083107_copy_title_to_question extends Migration
{
    public function safeUp(): bool
    {
        $field  = 'question';
        $entries = Entry::find()
            ->section('faq')
            ->type('default')
            ->all();

        if (!$entries) {
            echo "No entries found in the 'faq' section with the 'default' entry type.\n";
            return false;
        }

        $count = 0;
        foreach ($entries as $entry) {
            $title = $entry->title;
            if (!$entry->getFieldValue($field) && $title) {
                // Shorten the title if it's too long
                if (mb_strlen($title) > 255) {
                    $title = mb_substr($title, 0, 253) . '…';
                }

                // Set the field value
                $entry->setFieldValue($field, $title);

                // Save if field value has been set
                if ($entry->getFieldValue($field) === $title) {
                    Craft::$app->getElements()->saveElement($entry);
                    $count++;
                }
            }
        }

        echo "Updated $count entries\n";
        return true;
    }

    public function safeDown(): bool
    {
        echo "m250424_083107_copy_title_to_question cannot be reverted.\n";
        return true;
    }
}
