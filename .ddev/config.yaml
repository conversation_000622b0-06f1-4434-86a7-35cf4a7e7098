name: waarderingskamer
type: craftcms

docroot: web
php_version: "8.2"
webserver_type: nginx-fpm
router_http_port: "80"
router_https_port: "443"
xdebug_enabled: false
additional_hostnames:
  - search.waarderingskamer
additional_fqdns: []
database:
  type: mariadb
  version: "10.4"
nfs_mount_enabled: false
mutagen_enabled: false
use_dns_when_possible: true
composer_version: "2"
web_environment: []
nodejs_version: "18"
project_tld: nl.internal
upload_dirs: [web/uploads]
