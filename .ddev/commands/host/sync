#!/bin/bash

## Description: Syncs the project
## Usage: sync
## Example: "ddev sync"
# export SYNC_ENV=production # Set when live, so support always syncs from production
export SYNC_ENV=test

if [ ! -z "$1" ]; then
    export SYNC_ENV=$1
fi

echo "Running sync script on the host system."
date
echo "Start script"

#
# Pullit sql and files
#
echo "> Pulling database content... and files content..."
cd ./pullit/
sh pullit.sh --$SYNC_ENV
cd ../

date
echo "End script"
