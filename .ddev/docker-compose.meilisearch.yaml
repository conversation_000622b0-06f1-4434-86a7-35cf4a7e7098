version: '3.6'
services:
  meilisearch:
    container_name: ddev-${DDEV_SITENAME}-meilisearch
    image: getmeili/meilisearch:v1.1.1
    restart: always
    expose:
      - 7700
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: ${DDEV_APPROOT}
    environment:
      - MEILI_ENV=development
      - MEILI_MASTER_KEY=p9HS6FaqHaLiU4cVuXtxtzUDQOWC2dwGNLORWSkPOaE
      - VIRTUAL_HOST=$DDEV_HOSTNAME
      - HTTP_EXPOSE=7701:7700
      - HTTPS_EXPOSE=7700:7700
    volumes:
      - ./_volumes/meili_data:/meili_data
