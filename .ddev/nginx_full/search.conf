# Example configuration for a second docroot

#ddev-generated
# If you want to take over this file and customize it, rename it to <yourname>.conf,
# and remove the ddev-generated line above

server {
    # Set the docroot to where it belongs in the codebase
    root /var/www/html/;
    # Set the server_name so this config can be selected
    # You'll need additional_hostnames["seconddocroot"] in config.yaml for this to work
    server_name search.waarderingskamer.nl.internal;

    listen 80;
    listen 443 ssl;

    ssl_certificate /etc/ssl/certs/master.crt;
    ssl_certificate_key /etc/ssl/certs/master.key;


    index index.php index.htm index.html;

    # Disable sendfile as per https://docs.vagrantup.com/v2/synced-folders/virtualbox.html
    sendfile off;
    error_log /dev/stdout info;
    access_log /var/log/nginx/access.log;

    location / {
      gzip_static on;

      proxy_pass http://meilisearch:7700;
      proxy_set_header X-Real-IP  $remote_addr;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Scheme https;
      proxy_set_header Host $host;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto https;
      proxy_set_header X-Forwarded-Port 443;
      proxy_set_header X-Secure on;
      proxy_set_header Accept-Encoding "";
      proxy_set_header Connection "";
      proxy_hide_header Vary;
      proxy_http_version 1.1;
      proxy_intercept_errors on;
      proxy_read_timeout 7200s;
      proxy_send_timeout 7200s;
    }

    include /etc/nginx/common.d/*.conf;
    include /mnt/ddev_config/nginx/*.conf;
}
