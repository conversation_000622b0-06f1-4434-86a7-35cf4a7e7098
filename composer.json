{"name": "redkiwi/waarderingskamer", "description": "Website for waarderingskamer", "autoload": {"psr-4": {"organization\\": "modules/organization/", "modules\\twigextender\\": "modules/twig-extender/", "search\\": "modules/search/", "modelupload\\": "modules/model-upload/", "removemetadata\\": "modules/remove-metadata/src/", "modules\\documentresolver\\": "modules/document-resolver/"}}, "autoload-dev": {"psr-4": {"App\\TwigCs\\": ".tools/TwigCs"}}, "config": {"allow-plugins": {"craftcms/plugin-installer": true, "yiisoft/yii2-composer": true, "phpro/grumphp": true, "php-http/discovery": true}, "optimize-autoloader": true, "platform": {"php": "8.2"}, "sort-packages": true}, "repositories": {"redkiwi": {"type": "composer", "url": "https://composer.toolscloud.nl"}, "0": {"type": "composer", "url": "https://composer.craftcms.com", "canonical": false}}, "require": {"born05/craft-assetusage": "3.3.0", "craftcms/cms": "^4.5.5", "craftcms/generator": "^1.6.1", "craftcms/redactor": "^3.1.0", "digitalpulsebe/craft-formie-friendly-captcha": "^2.1.1", "doublesecretagency/craft-inventory": "^3.1.0", "internetztube/craft-element-relations": "^3.0", "mmikkel/cp-field-inspect": "^1.4.4", "nystudio107/craft-retour": "^4.1.18", "nystudio107/craft-seomatic": "^4.1.2", "nystudio107/craft-templatecomments": "^4.0.2", "nystudio107/craft-vite": "^4.0.10", "putyourlightson/craft-sprig": "^2.11.0", "redkiwi/craft-base": "^1.0", "redkiwi/craft-meilisearch": "^4.4.3", "sebastianlenz/linkfield": "^2.1.5", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.6", "spicyweb/craft-neo": "^4.2.10", "spicyweb/craft-quick-field": "^2.0.8", "trendyminds/craft-palette": "^4.1.1", "twig/intl-extra": "v3.8.0", "twig/string-extra": "v3.8.0", "verbb/formie": "^2.1.26", "verbb/icon-picker": "^2.0.18", "verbb/image-resizer": "^3.0.12", "verbb/smith": "^2.0.1", "verbb/super-table": "^3.0.14", "vlucas/phpdotenv": "^v5.5.0", "wbrowar/craft-admin-bar": "^4.3.0", "ext-zip": "*"}, "require-dev": {"friendsoftwig/twigcs": "^6.2.0", "nystudio107/craft-autocomplete": "^1.11.1", "php-parallel-lint/php-parallel-lint": "^v1.3.2", "phpro/grumphp": "^2.6", "studio-stomp/phpstan-craftcms": "dev-main", "yiisoft/yii2-shell": "^2.0.5"}, "scripts": {"lint": "vendor/bin/twigcs templates --ruleset \"\\App\\TwigCs\\TwigCsRuleset\"", "twigcs": "vendor/bin/twigcs templates --ruleset \"\\App\\TwigCs\\TwigCsRuleset\" --reporter junit > storage/twig-cs-report.xml", "phpstan": "php -d memory_limit=-1 vendor/bin/phpstan analyse modules", "craft-update": ["@pre-craft-update", "@post-craft-update"], "pre-craft-update": [], "post-craft-update": ["@php craft install/check && php craft up --interactive=0 || exit 0", "@php craft install/check && php craft clear-caches/all --interactive=0 || exit 0", "@php craft install/check && php craft invalidate-tags/all --interactive=0 || return 0"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php craft setup/welcome"], "pre-update-cmd": "@pre-craft-update", "pre-install-cmd": "@pre-craft-update", "post-update-cmd": "@post-craft-update", "post-install-cmd": "@post-craft-update"}}