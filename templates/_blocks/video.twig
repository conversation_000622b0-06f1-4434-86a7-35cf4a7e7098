{% set video = block.videoSrc | default() %}
{% set subtitle = block.videoSubtitles.one() ?? null %}

{% if video %}
    <section>
        <div class="m-auto container container--padding w-full {{ entry.type == 'theme' ? 'w-full' : 'max-w-4xl' }}">
            {% include "_parts/video" with {
                video: video,
                subtitle: subtitle
            } %}
        </div>
    </section>
{% endif %}
