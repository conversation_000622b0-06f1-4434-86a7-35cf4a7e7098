{% set image = block.image.one() | default() %}

<section class="flex flex-col gap-10 md:gap-5 container container--padding {{ block.textImageLayout == 'text-image' ? 'md:flex-row-reverse' : 'md:flex-row' }}">
    <div class="flex {{ block.textImageLayout == 'text-image' ? 'md:justify-end' : 'md:justify-start' }} justify-center md:w-1/2">
        {% include "_parts/picture.twig" with {
            loading: 'lazy',
            class: 'block max-w-full leaf-shape',
            image,
            width: 658,
            height: 624,
        } %}
    </div>
    <div class="flex flex-col justify-center items-start gap-6 md:w-1/2 md:px-4 lg:px-10">
        {% include '_blocks/wysiwyg.twig' with {
            class: 'items-start',
        } %}

        {% if block.linkButton %}
            <div class="container flex">
                {{ block.linkButton.getLink({
                    class: 'btn btn--primary text-center md:text-left w-full md:w-auto',
                }) }}
            </div>
        {% endif %}
    </div>
</section>
