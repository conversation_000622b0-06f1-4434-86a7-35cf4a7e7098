{% set bgColor = block.wysiwygBackground.value | default('') %}

{% if bgColor == "blue" %}
    {% set bgColor = "bg-secondary-100" %}
{% elseif bgColor == "turquoise" %}
    {% set bgColor = "bg-primary-100" %}
{% else %}
    {% set bgColor = "" %}
{% endif %}

<section class="container flex flex-col gap-10 md:gap-5 {{ entry.type.handle == 'theme' ? 'items-start' : 'items-center' }} {{ class | default('container--padding') }}">
    <div class="wysiwyg w-full  {{ entry.type.handle == 'theme' ? 'w-full' : 'max-w-4xl' }} {{ bgColor starts with 'bg' ? ' has-background p-4 lg:p-6 rounded-3xl ' ~ bgColor }}">
        {{ wysiwyg | default(block.wysiwyg) }}
    </div>
</section>
