{% set heading = block.heading %}
{% set button = block.ctaButton %}
{% set layout = entry.type == 'theme' ? 'medium' : block.ctaLayout %}
{% set image = block.image.one() | default() %}

{% macro button(link) %}
    <a {{ link.getLinkAttributes() }} {{ link.getType() == 'asset' ? 'download' }} class="btn btn--primary text-center md:text-left w-full md:w-max inline-block">
        {% if link.getType() == 'asset' %}
            {% include '_parts/icon.twig' with {
                'icon': 'downloaden',
                'class': 'w-5 h-5 mr-5'
            } %}
            {{ link.customText }}
        {% else %}
            {{ link.customText }}
            {% include '_parts/icon.twig' with {
                'icon': 'pijl-rechts',
                'class': 'w-5 h-5 ml-5'
            } %}
        {% endif %}
    </a>
{% endmacro %}

<section class="container container--padding flex justify-center">
    <div class="bg-primary-100 w-full
        {{ layout == 'wide' ? 'rounded-3xl p-8 lg:py-16 lg:px-28' }}
        {{ layout == 'medium' ? 'rounded-[2.5rem] max-w-5xl px-12 py-10' }}
        {{ layout == 'small' ? 'rounded-2xl max-w-xl p-6 flex flex-col gap-8' : 'flex flex-col md:flex-row md:items-center gap-10 md:gap-5' }}
    ">
        {% if layout == 'small' %}
            {% if heading is defined and heading is not empty %}
                <h2 class="h5">{{ heading }}</h2>
            {% endif %}

            {% if button is defined and button is not empty %}
                {{ _self.button(button) }}
            {% endif %}
        {% else %}
            <div class="md:w-1/2 {{ layout == 'wide' ? 'lg:pr-8' }} inline-flex flex-col gap-12">
                <div>
                    {% if heading is defined and heading is not empty %}
                        <h3 class="pb-2">{{ heading }}</h3>
                    {% endif %}

                    {% include '_blocks/wysiwyg.twig' with ({
                        class: 'p-0'
                    }) %}
                </div>

                {% if button is defined and button is not empty %}
                    {{ _self.button(button) }}
                {% endif %}
            </div>

            {% if image is defined and image is not empty %}
                <div class="flex justify-center md:w-1/2">
                    {% include "_parts/picture.twig" with {
                        class: 'block max-w-full',
                        image
                    } %}
                </div>
            {% endif %}
        {% endif %}
    </div>
</section>
