<section class="container space-y-4 container--padding">
    {% if block.heading is defined and block.heading is not empty %}
        <h2 class="h4">{{ block.heading }}</h2>
    {% endif %}

    {% if block.wysiwyg %}
        <div class="wysiwyg">
            {{ block.wysiwyg }}
        </div>
    {% endif %}

    {% macro errorList(errors) %}
        {% if errors %}
            {{ ul(errors, {class: 'errors'}) }}
        {% endif %}
    {% endmacro %}

    {% set submission = submission ?? null %}

    {% if block.formField %}
        {{ craft.formie.renderForm(block.formField.one()) }}
    {% endif %}

    {% if craft.app.session.hasFlash('notice') %}
        <p class="message notice text-primary-700">{{ craft.app.session.getFlash('notice') }}</p>
    {% elseif craft.app.session.hasFlash('error') %}
        <p class="message error">{{ craft.app.session.getFlash('error') }}</p>
    {% endif %}

</section>
