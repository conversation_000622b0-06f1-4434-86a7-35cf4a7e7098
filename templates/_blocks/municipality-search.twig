{% extends '_layouts/block.twig' %}

{% set blockClass = 'container container--padding flex gap-4 flex-wrap' %}
{% set blockElement = 'section' %}
{% set searchIndex = getenv('MEILISEARCH_INDEX_PREFIX') ~ '_' ~ craft.app.language ~ '_' ~ craft.app.sites.getCurrentSite().handle ~ '_organization' %}

{% block blockContent %}
    <div id="municipality-search" class="grid grid-cols-1 gap-6 p-6 md:py-8 lg:py-10 md:grid-cols-12 grow bg-primary-100 rounded-3xl"
        x-data="municipalitySearch({{ {searchIndex, key: getenv('MEILISEARCH_CLIENT_KEY'), host: getenv('MEILISEARCH_CLIENT_HOST'), lang: craft.app.language} | json_encode }})">
        <div class="flex justify-center -mb-[33%] md:mb-0 md:col-start-2 md:col-end-5 lg:col-end-6">
            <img width="445" height="530" src="{{ craft.vite.asset("images/country-map.svg", true) }}" loading="lazy" alt="">
        </div>

        <div x-data="{open: false}" class="flex items-center md:col-start-6 md:col-end-12 lg:col-start-7">
            <div class="flex flex-col gap-6 bg-white w-full rounded-3xl">
                <div class="p-8 pt-8 pb-0">
                    <h2 class="h3 max-w-md">{{ block.heading }}</h2>
                    <p class="pt-2 p-small">{{ block.wysiwyg }}</p>
                </div>

                <div class="municipality-form-wrapper group" data-municipality-form-wrapper :data-active="open">
                    <form role="search" class="flex flex-row w-full" aria-label="{{ 'Municipalities' | t }}">
                        <button type="button" class="max-sm:group-data-[active]:block hidden w-[50px] shrink-0" data-close>
                            <span class="sr-only">{{ 'Close' | t }}</span>
                            <span>
                                {% include '_parts/icon.twig' with {
                                    'icon': 'x',
                                    'class': 'w-5 h-5 text-secondary-500'
                                } %}
                            </span>
                        </button>
                        <div class="relative flex flex-row grow">
                            <label for="search-municipality" class="sr-only">{{ 'Search by municipality' | t }}</label>
                            <input
                                id="search-municipality"
                                type="text"
                                name="query"
                                class="w-full px-4 py-3 text-black placeholder-black border placeholder-opacity-70 border-secondary rounded-2xl text-opacity-70"
                                placeholder="{{ 'Search by municipality' | t }}"
                                x-on:input.change="$store.municipalitySearch.getResults($event.target.value)"
                                @keyup.enter="$store.municipalitySearch.goToRoute($store.municipalitySearch.results[0].route)"
                            />
                            <span class="absolute right-0 p-3 -translate-y-1/2 top-1/2" @click="$store.municipalitySearch.goToRoute($store.municipalitySearch.results[0].route)">
                                <span class="sr-only">{{ 'Search by municipality' | t }}</span>
                                <span>
                                    {% include '_parts/icon.twig' with {
                                        'icon': 'zoeken',
                                        'class': 'w-5 h-5 text-secondary-500'
                                    } %}
                                </span>
                            </span>
                        </div>
                    </form>
                    <template x-if="!$store.municipalitySearch.hasNoResults()">
                        <ul class="autocomplete-results">
                            <template x-for="result in $store.municipalitySearch.results" :key="result.uid">
                                <li class="font-medium"><a :href="result.route" class="block px-6 py-3 no-underline text-secondary-500 underline-offset-8 hover:underline hover:text-primary-700 visited:text-secondary-500"><span x-text="result.title"></span></a></li>
                            </template>
                        </ul>
                    </template>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
