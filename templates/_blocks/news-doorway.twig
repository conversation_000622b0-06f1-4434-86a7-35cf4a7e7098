{% extends '_layouts/block.twig' %}

{% set blockClass = 'container container--padding flex flex-col gap-6' %}
{% set blockElement = 'section' %}
{% set locale = craft.app.i18n.getLocaleById(currentSite.language | slice(0, 2)) %}

{% block blockContent %}
    <h2>{{ block.heading }}</h2>
    <div class="grid grid-cols-1 sm:grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-6">
        {% for item in block.newsDoorwayItems.with(["entry"]).all() %}
            {% set entry = item.entry[0] ?? null %}
            {% if entry %}
                <div class="relative flex flex-col col-span-1 overflow-hidden transition-colors bg-secondary-100 rounded-2xl group">

                    <div class="flex flex-col items-start justify-between gap-6 p-4 grow">
                        <div class="flex items-center gap-4">
                            <span class="px-3 py-1 border rounded-full border-secondary-500 text-secondary-500 w-min">{{ entry.section.handle | capitalize | t }}</span>
                            <span class="text-secondary-500">{{ entry.dateCreated | format_datetime('long', 'none', locale=locale) }}</span>
                        </div>
                        <div>
                            <h3 class="mb-1 h5">
                                <a
                                    href="{{ entry.url }}"
                                    class="
                                        no-underline hover:text-current focus-visible:text-current
                                        after:absolute after:inset-0 after:w-full after:h-full
                                    "
                                >
                                    {{ item.heading }}
                                </a>
                            </h3>
                            {% if item.subtitle %}
                                <p class="line-clamp-3 text-secondary-900">{{ item.subtitle }}</p>
                            {% endif %}
                        </div>
                        <div
                            aria-hidden="true"
                            class="btn btn--secondary"
                        >
                            {{ 'Read more' | t }}

                            {% include '_parts/icon.twig' with {
                                'icon': 'pijl-rechts',
                                'class': 'ml-2 w-5 h-5'
                            } %}
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>

    {% if block.linkButton is defined and not block.linkButton.isEmpty() %}
        <div class="flex justify-center pt-4">
            <a{{ block.linkButton.getLinkAttributes() }} class="w-full text-center btn btn--quartenary md:text-left md:w-auto">
                {{ block.linkButton.getCustomText('Read more' | t) }}
                {% include '_parts/icon.twig' with {
                    'icon': 'pijl-rechts',
                    'class': 'ml-2 w-5 h-5'
                } %}
            </a>
        </div>
    {% endif %}

{% endblock %}
