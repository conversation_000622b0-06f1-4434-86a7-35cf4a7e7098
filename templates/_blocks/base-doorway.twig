{% extends '_layouts/block.twig' %}

{% set blockClass = 'container container--padding flex flex-col gap-4' ~ (containsQuickLinks is defined and not containsQuickLinks ? ' !items-stretch' : '') %}
{% set blockElement = 'section' %}

{% block blockContent %}

    <div class="flex flex-col gap-4 lg:flex-row lg:justify-between lg:items-center items-[initial] items-[]">
        <h2>{{ block.heading }}</h2>

        {% if block.linkButton is defined and not block.linkButton.isEmpty() %}
            <div class="flex justify-center max-lg:hidden">
                <a{{ block.linkButton.getLinkAttributes() }} class="w-full text-center btn btn--primary md:text-left md:w-auto">
                    {{ block.linkButton.getCustomText('Read more' | t) }}
                    {% include '_parts/icon.twig' with {
                        'icon': 'pijl-rechts',
                        'class': 'ml-2 w-5 h-5'
                    } %}
                </a>
            </div>
        {% endif %}
    </div>

    <div class="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-6">
        {% for item in block.baseDoorwayItems.with(["entry", "image"]).all() %}
            <div class="relative flex flex-col col-span-1 overflow-hidden transition-colors bg-secondary-100 rounded-2xl group">

                {% macro picture(image) %}
                    {% include "_parts/picture.twig" with {
                        class: 'block w-full h-[190px] rounded-br-shape group-hover:-translate-y-[10px] group-hover:scale-105 transition-all',
                        image,
                        width: 362,
                        height: 190,
                    } %}
                {% endmacro %}

                {% if item.image[0] is defined %}
                    {{ _self.picture(item.image[0]) }}
                {% elseif item.entry[0] is defined and item.entry[0].image.one() | default(false) %}
                    {{ _self.picture(item.entry[0].image.one()) }}
                {% endif %}

                <div class="flex flex-col items-start justify-between gap-6 p-4 grow">
                    <div>
                        <h3 class="mb-1 h4">
                            <a
                                {{ item.entry.getLinkAttributes() }}
                                class="
                                    no-underline hover:text-current focus-visible:text-current
                                    after:absolute after:inset-0 after:w-full after:h-full
                                "
                            >
                                {{ item.heading }}
                            </a>
                        </h3>
                        {% if item.subtitle %}
                            <p>{{ item.subtitle }}</p>
                        {% endif %}
                    </div>
                    <div
                        aria-hidden="true"
                        class="btn btn--secondary"
                    >
                        {{ 'Click here' | t }}

                        {% include '_parts/icon.twig' with {
                            'icon': 'pijl-rechts',
                            'class': 'ml-2 w-5 h-5'
                        } %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

{% endblock %}
