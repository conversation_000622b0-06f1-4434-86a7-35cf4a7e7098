{% extends '_layouts/block.twig' %}

{% set blockClass = 'container container--padding flex gap-4 flex-wrap' %}
{% set blockElement = 'section' %}

{% block blockContent %}
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-12 grow">
        <h2 class="h3 {{ entry.type == 'theme' ? 'col-span-full' : 'lg:col-span-5' }}">{{ block.heading }}</h2>

        <div class="{{ entry.type == 'theme' ? 'col-span-full' : 'lg:col-span-7' }}">
            {% include "_parts/accordion/set" with {
                items: block.faqItems.all()
            } %}
        </div>
    </div>
{% endblock %}
