{% extends '_layouts/block.twig' %}

{% set blockClass = 'container container--padding flex flex-col gap-4' %}
{% set blockElement = 'section' %}

{% block blockContent %}
    <h2>{{ block.heading }}</h2>
    <div class="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-6">
        {% for item in block.themeDoorwayItems.with(["entry"]).all() %}
            <div class="relative flex flex-col col-span-1 text-secondary-500 p-4 transition-colors bg-primary-100 md:bg-transparent hover:bg-primary-100 rounded-2xl">
                {% include '_parts/icon.twig' with {
                    'icon': item.icon.label,
                    'class': 'w-14 h-14 mb-6'
                } %}

                <h3 class="mb-1 h5">
                    <a
                        {{ item.entry.getLinkAttributes() }}
                        class="
                            no-underline hover:text-current focus-visible:text-current
                            after:absolute after:inset-0 after:w-full after:h-full
                        "
                    >
                        {{ item.heading }}
                    </a>
                </h3>
                {% if item.subtitle %}
                    <p>{{ item.subtitle }}</p>
                {% endif %}
            </div>
        {% endfor %}
    </div>

{% endblock %}
