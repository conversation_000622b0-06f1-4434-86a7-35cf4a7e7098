{% set image = block.image.one() | default() %}
{% set imageMobile = block.imageResponsive.one() | default() %}

{% if image %}
    {% do image.setTransform("imageMedium") %}

    <picture class="container container--padding flex justify-center">
        {% if imageMobile %}
            {% do imageMobile.setTransform("imageSmall") %}
            <source media="(max-width: 768px)" srcset="{{ imageMobile.getSrcset(['1.5x', '2x', '3x']) }}" />
        {% endif %}

        <img
            loading="lazy"
            class="object-contain w-full rounded-3xl"
            srcset="{{ image.getSrcset(['1.5x', '2x', '3x']) }}"
            src="{{ image.getUrl() }}"
            alt="{{ image.alt | default('') }}"
        />
    </picture>
{% endif %}