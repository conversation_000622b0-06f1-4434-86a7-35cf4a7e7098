{% extends '_layouts/base.twig' %}

{% block content %}
    <div class="container flex flex-col gap-4 pb-20 search__wrapper container--padding">
        <div class="flex flex-col" x-data="search({{ {hits: [], search: query, key: api_key, host, searchIndex, lang, facetDistribution: []} | json_encode }})">
            <div class="grid grid-cols-1 lg:grid-cols-12 py-14">
                <div class="flex flex-col gap-8 col-span-full lg:col-span-9 lg:col-start-4">
                    <h1 class="inline-flex gap-x-3">{{ 'You searched for:' | t }} <template x-if="$store.search.searchTerm"><span class="search-query" x-text="$store.search.searchTerm"></span></template></h1>
                </div>
            </div>

            <div class="grid grid-cols-1 gap-6 lg:grid-cols-12">
                <div class="flex flex-col gap-4 lg:hidden">
                    <div class="flex flex-row items-center grow drop-shadow-lg">
                        <label for="search-bar" class="sr-only">{{ 'Search' | t }}</label>
                        <input id="search-bar" type="search" placeholder="{{ 'Search' | t }}" x-on:input.change="$store.search.getResults($event.target.value)" class="placeholder-black placeholder-opacity-70 w-full border-secondary-300 border-opacity-50 border border-r-0 rounded-l-[1rem] py-5 px-4 text-black text-opacity-70">
                        <div class="rounded-r-[1rem] border-secondary-300 max-h-[66px] border-opacity-50 border border-l-0 py-5 px-3 bg-white">
                            {% include '_parts/icon.twig' with {
                                'icon': 'zoeken',
                                'class': 'w-5 h-5 text-secondary-500'
                            } %}
                        </div>
                    </div>
                    <button class="btn btn--quartenary" aria-controls="filter-search" aria-expanded="false">
                        {% include '_parts/icon.twig' with {
                            'icon': 'settings-2',
                            'class': 'w-5 h-5 mr-5'
                        } %}
                        {{ 'Filter results' | t }}
                    </button>
                </div>
                <div id="filter-search" class="flex-col hidden data-[expanded=true]:flex lg:col-span-3 gap-4 lg:pr-8 lg:flex">
                    <span class="pb-4 border-b border-secondary-500 h4">{{ 'Filter search results' | t }}</span>

                    <div class="flex flex-col gap-6">
                        <template x-for="facet in $store.search.facetDistribution" :key="facet.name">
                            <fieldset class="flex flex-col gap-2 text-secondary-900">
                                <legend class="label text-secondary-500" x-text="facet.label"></legend>
                                <template x-if="facet.values.length > 0">
                                    <ul class="flex flex-col">
                                        <template x-for="option in facet.values" :key="option.name">
                                            <li class="px-2 -mx-2 rounded hover:bg-primary-100">
                                                <label class="flex items-center w-full gap-2 py-1 cursor-pointer">
                                                    <input type="checkbox" x-on:input.change="(event) => $event.target.checked ? $store.search.setFilter({value: option.name, type: facet.name}) : $store.search.removeFilter(facet.name)" :value="option.name" x-modal class="w-4 h-4">
                                                    <span x-text="$store.search.getHandle(option.name)"></span>
                                                    <span class="opacity-60">
                                                        (<span x-text="option.count"></span>)
                                                    </span>
                                                </label>
                                            </li>
                                        </template>
                                    </ul>
                                </template>
                                <template x-if="facet.values.length === 0">
                                    <span>{{ 'No filter options' | t }}</span>
                                </template>
                            </fieldset>
                        </template>
                    </div>

                </div>
                <div class="flex flex-col gap-8 lg:col-span-9">
                    <div class="flex-row hidden gap-4 lg:flex">
                        <div class="flex flex-row items-center grow drop-shadow-lg">
                            <label for="search-bar-2" class="sr-only">{{ 'Search' | t }}</label>
                            <input id="search-bar-2" type="search" placeholder="{{ 'Search' | t }}" x-on:input.change="$store.search.getResults($event.target.value)" class="placeholder-black placeholder-opacity-70 w-full border-secondary-300 border-opacity-50 border border-r-0 rounded-l-[1rem] py-5 px-4 text-black text-opacity-70"
                            />
                            <div class="rounded-r-[1rem] border-secondary-300 border-opacity-50 border border-l-0 py-5 px-3 bg-white">
                                {% include '_parts/icon.twig' with {
                                    'icon': 'zoeken',
                                    'class': 'w-5 h-5 text-secondary-500'
                                } %}
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="hidden not-sr-only lg:flex">{{ 'Sort by' | t }}</span>
                            <div class="relative">
                                <select aria-label="{{ 'Sort by' | t }}" class="pl-6 pr-16 appearance-none btn btn--quartenary peer" x-on:input.change="$store.search.sortBy($event.target.value)">
                                    <option disabled selected value="">{{ 'Sort by' | t }}</option>
                                    <option value="">{{ 'Relevance' | t }}</option>
                                    <option value="postDate:desc">{{ 'Date' | t }}</option>
                                    <option value="title:asc">{{ 'Alphabetical (A-Z)' | t }}</option>
                                </select>
                                {% include '_parts/icon.twig' with {
                                    'icon': 'chevron-omlaag',
                                    'class': 'w-6 h-6 absolute p-1 right-4 top-1/2 transform -translate-y-1/2 text-secondary-500 peer-focus:text-white'
                                } %}
                            </div>
                        </div>
                    </div>

                    <div class="min-h-[50vh]">
                        <div class="grid w-full grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6" aria-live="polite">
                            <template x-for="result in $store.search.results" :key="result.uid">
                                <template x-if="result.type || result.section">
                                    <article class="relative flex flex-col-reverse w-full p-6 bg-gray-300 group rounded-2xl md:p-8 lg:p-0 lg:flex-row-reverse">
                                        <div class="gap-4 grow lg:flex lg:p-4 lg:flex-col lg:justify-between">
                                            <span class="px-3 py-1 border rounded-full border-secondary-500 text-secondary-500 w-min" x-text="$store.search.getHandle((result.type ?? result.section).handle)"></span>

                                            <div>
                                                <h2 class="mt-8 mb-2 h4 lg:mt-0" x-text="result.title"></h2>

                                                <template x-if="result.doorwayText" :key="result.uid">
                                                    <div class="mb-6 line-clamp-3" x-html="result.doorwayText || ''"></div>
                                                </template>
                                            </div>

                                            <div class="inline-flex self-end">
                                                <a
                                                    :href="(result.url ?? result.route)"
                                                    class="absolute top-0 bottom-0 left-0 right-0 z-10 hover:cursor-pointer text-secondary-500"
                                                    :aria-label="result.title"
                                                ></a>
                                                {% include '_parts/icon.twig' with {
                                                    'icon': 'pijl-rechts',
                                                    'class': 'w-6 h-6 ml-2 group-hover:text-secondary-300'
                                                } %}
                                            </div>
                                        </div>
                                    </article>
                                </template>
                            </template>

                            <template x-if="$store.search.hasNoResults()">
                                <p>{{ 'No results found' | t }}</p>
                            </template>
                        </div>
                    </div>

                    <template x-if="$store.search.hasMorePages()">
                        <button class="flex justify-center btn btn--quartenary" x-on:click="$store.search.getMoreResults()">
                            <template x-if="$store.search.isLoading === true">
                                <div>
                                    <span>{{ 'Loading...' | t }}</span>
                                    {% include '_parts/icon.twig' with {
                                        'icon': 'Pijlen-Spinner',
                                        'class': 'ml-2 w-5 h-5 animate-spin '
                                    } %}
                                </div>
                            </template>
                            <template x-if="$store.search.isLoading === false">
                                <div>
                                    <span>{{ 'Show more results' | t }}</span>
                                    {% include '_parts/icon.twig' with {
                                        'icon': 'pijl-omlaag',
                                        'class': 'ml-2 w-5 h-5'
                                    } %}
                                </div>
                            </template>
                        </button>
                    </template>
                </div>
            </div>
        </div>
    </div>

{% endblock %}
