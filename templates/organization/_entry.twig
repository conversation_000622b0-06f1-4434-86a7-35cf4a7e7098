{% extends '_layouts/base.twig' %}

{% macro li(id, icon, title) %}
    <li class="border-primary-100 max-lg:border-t">
        <a
            href="#{{ id }}"
            class="
                flex flex-row items-center py-3.5 px-5 no-underline transition-all text-secondary-900
                hover:font-bold hover:text-secondary
                focus-visible:font-bold focus-visible:text-secondary
                max-lg:w-full
                lg:p-2 lg:pl-0
            "
        >
            <span>
                {% include '_parts/icon.twig' with {'icon': icon, 'class': 'w-6 h-6'} %}
            </span>
            <span class="ml-4">{{ title }}</span>
        </a>
    </li>
{% endmacro %}

{% block content %}
    <div class="container pb-36 container--padding">
        <div class="grid grid-cols-1 lg:grid-cols-sidebar lg:grid-areas-sidebar">
            <div
                class="relative flex flex-col lg:grid-in-sidebar"
                x-data="{ open: false }"
                x-trap="open"
            >
                <button class="z-40 w-full font-bold hidden justify-between items-center lg:p-6 py-4 px-[1.125rem] no-underline text-secondary-900 bg-primary-500 group
                        hover:text-secondary-500 visited:text-secondary-900 focus-visible:text-primary-700
                        max-lg:fixed max-lg:left-0 max-lg:top-[3.85rem] max-lg:inline-flex quicklink-controls"
                        aria-controls="quicklink"
                        aria-expanded="false"
                        @click="open = !open"
                >
                    <span class="flex gap-4">
                        {% include '_parts/icon.twig' with {
                            'icon': 'lijsten',
                            'class': 'w-6 h-6'
                        } %}

                        {{ 'Quick view' | t }}
                    </span>

                    {% include '_parts/icon.twig' with {
                        'icon': 'chevron-omlaag',
                        'class': 'w-6 h-6 group-aria-expanded:rotate-180'
                    } %}
                </button>

                <ul id="quicklink" class="lg:pr-6 sticky overflow-hidden top-14 left-0 transition-all w-full z-40
                        max-lg:fixed max-lg:top-[7.3rem] max-lg:invisible max-lg:opacity-0 max-lg:bg-white max-lg:shadow-xl
                        -translate-y-2 data-[expanded=true]:visible data-[expanded=true]:opacity-100 data-[expanded=true]:translate-y-0">
                    {{ _self.li('woz-information', 'informatie', "WOZ information" | t) }}
                     {% if organization.association %}
                        {{ _self.li('partnership', 'inwoners', "This partnership carries out the WOZ for:" | t) }}
                    {% endif %}
                    {{ _self.li('judgement', 'ster', "Judgement" | t) }}
                    {{ _self.li('recent-studies', 'downloaden', "Recent studies" | t) }}
                </ul>
            </div>

            <div class="flex flex-col gap-20 lg:grid-in-content">
                <div class="flex flex-col gap-10">
                    {% if organization.association %}
                        <span class="text-primary-500 label">{{ 'Collaboration between' | t }} {{ associatedOrganizations | length }} {{ 'municipalities' | t }} </span>
                    {% endif %}

                    {% if affiliated %}
                        <div class="flex flex-col lg:flex-row lg:items-center">
                            <span class="text-[0.875rem] text-secondary-500">{{ 'This municipality has chosen to carry out the WOZ at:' | t }}</span>
                            <a href="{{ affiliated.route }}" class="btn btn--quartenary px-3 py-2 text-[0.875rem] w-fit max-lg:mt-2 lg:ml-2">{{ affiliated.title }}</a>
                        </div>
                    {% endif %}

                    <h2 id="woz-information" class="scroll-mt-20 max-lg:scroll-mt-44">{{ 'WOZ information' | t }} {{ organization.title }}</h2>
                </div>

                <section id="market-development-houses" data-chart-section class="flex flex-col gap-6 scroll-mt-20 max-lg:scroll-mt-44">
                    {% macro chart(orgId, nationalAverage, chart, subtitle, subtitleValue) %}
                        <div class="flex flex-col gap-10 p-4 bg-primary-100 rounded-2xl justify-between @container">
                            <div class="flex items-center justify-between">
                                <h4>{{ chart.abbreviation }}</h4>

                                <a href="{{ "%s/%d/%s" | format("/download/chart", orgId, chart.code) }}" class="font-bold text-secondary-500 hover:text-secondary-500 hover:opacity-80 shrink-0">
                                    <span class="sr-only">{{ 'Download as csv' | t }}</span>
                                    {% include '_parts/icon.twig' with {
                                        'icon': 'downloaden',
                                        'class': 'w-6 h-6'
                                    } %}
                                </a>
                            </div>

                            <div
                                id="{{ chart.code }}"
                                class="w-full relative chart-container h-[65cqw] @xs:h-[50cqw]"
                                data-chart-values="{{ chart.chartData | json_encode }}"
                                data-chart-national-average="{{ nationalAverage.chartData | json_encode }}"
                                data-chart-label="{{ chart.abbreviation }}"
                                data-chart-x-label="{{ 'Reference date' | t }}"
                                aria-hidden="true"
                            >
                                <canvas aria-label="{{ chart.abbreviation }} {{ 'per year' | t }}" role="img" tabindex="0"></canvas>
                            </div>

                            {# dialog #}
                            <div x-data="{ open: false }" @keydown.escape="open = false">
                                <button
                                    aria-controls="dialog-{{ chart.code }}"
                                    @click="$refs.{{ chart.code }}.showModal(); open = true"
                                    class="btn btn--secondary"
                                >
                                    {{ 'View original data' | t }}<span class="sr-only"> {{ 'of' | t }} {{ chart.abbreviation }}</span>
                                     {% include '_parts/icon.twig' with {
                                        'icon': 'pijl-rechts',
                                        'class': 'ml-2 w-5 h-5'
                                    } %}
                                </button>
                                <dialog
                                    id="dialog-{{ chart.code }}"
                                    x-ref="{{ chart.code }}"
                                    class="@container w-[calc(100vw-2rem)] max-w-3xl p-4 rounded-2xl backdrop:bg-black/50 backdrop:backdrop-blur-md"
                                    x-trap.inert.noscroll="open"
                                >
                                    <div class="prose max-w-none">
                                        <h5>{{ chart.abbreviation }} {{ 'per year' | t }}</h5>
                                        {% set type = chart.code == 'GWWA' ? 'euro' : 'prcnt' %}

                                        <div class="grid grid-cols-1 gap-8 @lg:grid-cols-2 @lg:gap-12">
                                            {{ _self.dataTable(chart.abbreviation, chart.chartData, type) }}
                                            {{ _self.dataTable('National avarage' | t, nationalAverage.chartData, type) }}
                                        </div>
                                        <form method="dialog" class="flex justify-end">
                                            <button @click="open = false" class="btn btn--primary">
                                                {{ 'Close' | t }}
                                            </button>
                                        </form>
                                    </div>
                                </dialog>
                            </div>
                        </div>
                    {% endmacro %}

                    {% macro dataTable(title, data, type) %}
                        <table>
                            <caption class="font-bold text-secondary text-sm text-left">{{ title }}</caption>
                            <thead>
                                <tr>
                                    <th>{{ 'Reference date' | t }}</th>
                                    <th>{{ type == 'euro' ? 'Euro' : 'Percentage' | t }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in data %}
                                    <tr>
                                        <td><time datetime="{{ row.date | date("Y-m-d", "Europe/Amsterdam") }}">{{ row.date | date("d-m-Y", "Europe/Amsterdam") }}</time></td>
                                        <td>
                                            {% if type == 'euro' %}
                                                € {{ (row.value * 1000) | number_format(0, ',', '.') }}
                                            {% else %}
                                                {{ row.value | number_format(1, ',', '.') }}%
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% endmacro %}

                    <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                        {% set locale = craft.app.i18n.getLocaleById(currentSite.language | slice(0, 2)) %}
                        {% if organization.charts.containsKey("GWWA") %}
                            {{ _self.chart(organization.identifier, nationalAverage.charts.get("GWWA"), organization.charts.get("GWWA"), "", ((organization.charts.get("GWWA").values | last) * 1000) | format_currency('EUR', {fraction_digit: 0}, locale=locale)) }}
                        {% endif %}
                        {% if organization.charts.containsKey("GMW") %}
                            {{ _self.chart(organization.identifier, nationalAverage.charts.get("GMW"), organization.charts.get("GMW"), "", ((organization.charts.get("GMW").values | last) / 100) | format_percent_number({fraction_digit: 1})) }}
                        {% endif %}
                    </div>
                </section>

                {% include "_parts/organization/info-and-statistics" with {
                    organization: organization,
                } only %}

                {% if organization.association %}
                    <section id="partnership" class="scroll-mt-20 max-lg:scroll-mt-44">
                        <h3>{{ "This partnership carries out the WOZ for:" | t }}</h3>
                        <div class="grid w-full grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6 mt-8">
                            {% for association in associatedOrganizations %}
                                <article class="relative flex flex-col-reverse w-full p-6 bg-secondary-100 group rounded-2xl md:p-8 lg:p-0 lg:flex-row-reverse">
                                    <div class="gap-4 grow lg:flex lg:p-4 lg:flex-col lg:justify-between">
                                        <div>
                                            <h4 class="mt-8 mb-2 lg:mt-0">{{ association.name }}</h4>
                                        </div>

                                        <div class="inline-flex self-end">
                                            <a href="{{ association.route }}"
                                                class="absolute top-0 bottom-0 left-0 right-0 z-10 hover:cursor-pointer text-secondary-500"
                                                aria-label="{{ association.name }}"
                                            ></a>
                                            {% include '_parts/icon.twig' with {
                                                'icon': 'pijl-rechts',
                                                'class': 'w-6 h-6 ml-2 group-hover:text-secondary-300'
                                            } %}
                                        </div>
                                    </div>
                                </article>
                            {% endfor %}
                        </div>
                    </section>
                {% endif %}

                <section id="judgement" class="scroll-mt-20 max-lg:scroll-mt-44" class="flex flex-col gap-6">
                    <h3>{{ "Judgement explanation" | t }}</h3>
                    <p>{{ "judgement_part_1" | t({name: organization.title, rating: organization.rating, prefix: organization.association ? "" : "de"}) }}</p><br/>
                        {% switch organization.rating %}
                            {% case 1.0 %}
                            {% case 2.0 %}
                                <p>{{ "insufficient_judgement" | t }}</p>
                            {% case 3.0 %}
                                <p>{{ "sufficient_judgement" | t }}</p>
                            {% default %}
                                <p>{{ "good_judgement" | t }}</p>
                        {% endswitch %}
                    <br/><p>{{ "judgement_part_3" | t({name: organization.title, prefix: organization.association ? "" : "de"}) }}</p>
                </section>

                {% set locale = craft.app.i18n.getLocaleById(currentSite.language | slice(0, 2)) %}
                {% if organization.researches %}
                    <section id="recent-studies" class="flex flex-col gap-6 scroll-mt-20 max-lg:scroll-mt-44">
                        <h3>{{ "Recent studies" | t }}</h3>
                        <ul class="flex flex-col gap-6">
                            {% for research in organization.researches %}
                                {% include "_parts/doorways/list-doorway/item" with {
                                    title: research.title,
                                    url: "%s/%s" | format("/download/document", research.document.identifier),
                                    publicationDate: research.document.date,
                                    locale: locale,
                                } only %}
                            {% endfor %}
                        </ul>
                    </section>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
