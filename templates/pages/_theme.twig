{% extends '_layouts/base.twig' %}

{% block keyvisual %}
    <div class="mb-8 max-lg:mt-14">
        {% include "_parts/breadcrumbs.twig" %}
        {% include '_parts/keyvisual.twig' %}
    </div>
{% endblock %}

{% set blocks = entry.contentBlocks.all() %}

{% set quickLinks = blocks | filter(x => x.quickLinkTitle is not empty) %}
{% set containsQuickLinks = quickLinks | length > 0 %}

{% block content %}
    <div class="lg:container lg:container--padding">
        <div class="grid grid-cols-1 {{ containsQuickLinks ? 'lg:grid-areas-sidebar lg:grid-cols-sidebar' }}">
            {% if containsQuickLinks %}
                <div
                    class="relative flex flex-col lg:grid-in-sidebar"
                    x-data="{ open: false }"
                    x-trap="open"
                >
                    <button class="z-40 w-full font-bold hidden justify-between items-center lg:p-6 py-4 px-[1.125rem] no-underline text-secondary-900 bg-primary-500 group
                        hover:text-secondary-500 visited:text-secondary-900 focus-visible:text-primary-700
                        max-lg:fixed max-lg:left-0 max-lg:top-[3.85rem] max-lg:inline-flex quicklink-controls"
                        aria-controls="quicklink"
                        aria-expanded="false"
                        @click="open = !open"
                    >
                        <span class="flex gap-4">
                            {% include '_parts/icon.twig' with {
                                'icon': 'lijsten',
                                'class': 'w-6 h-6'
                            } %}

                            {{ 'Quick view' | t }}
                        </span>

                        {% include '_parts/icon.twig' with {
                            'icon': 'chevron-omlaag',
                            'class': 'w-6 h-6 group-aria-expanded:rotate-180'
                        } %}
                    </button>

                    <ul id="quicklink"
                        class="lg:pt-10 lg:pr-6 sticky overflow-hidden top-14 left-0 transition-all w-full z-40
                        max-lg:fixed max-lg:top-[7.3rem] max-lg:invisible max-lg:opacity-0 max-lg:bg-white max-lg:shadow-xl
                        -translate-y-2 data-[expanded=true]:visible data-[expanded=true]:opacity-100 data-[expanded=true]:translate-y-0"
                    >
                        {% for block in quickLinks %}
                            <li class="border-primary-100 max-lg:border-t">
                                <a
                                    href="#{{ block.quickLinkTitle | kebab }}"
                                    class="
                                        flex flex-row items-center py-3.5 px-5 no-underline transition-all text-secondary-900
                                        hover:font-bold hover:text-secondary
                                        focus-visible:font-bold focus-visible:text-secondary
                                        max-lg:w-full
                                        lg:p-2 lg:pl-0
                                    "
                                >
                                    <span>
                                        {% include '_parts/icon.twig' with {'icon': block.quickLinkIcon.label ?? 'woz-waarde', 'class': 'w-6 h-6'} %}
                                    </span>
                                    <span class="ml-4">{{ block.quickLinkTitle }}</span>
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}

            <div class="flex flex-col gap-8 {{ containsQuickLinks ? 'lg:grid-in-content' : '[&>*>*]:items-center' }}">
                {% include '_parts/content.twig' with {blocks, containsQuickLinks} %}
            </div>
        </div>
    </div>
{% endblock %}
