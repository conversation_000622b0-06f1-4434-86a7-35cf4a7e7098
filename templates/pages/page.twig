{% switch entry.type %}
    {% case "news" %}
        {% include "pages/_news.twig" %}

    {% case "publication" %}
        {% include "pages/_publication.twig" %}

    {% case "theme" %}
        {% include "pages/_theme.twig" %}

    {% case "contact" %}
        {% include "pages/_contact.twig" %}

    {% case "newsOverview" %}
        {% include "pages/_news-overview.twig" %}

    {% default %}
        {% include "pages/_page.twig" %}
{% endswitch %}
