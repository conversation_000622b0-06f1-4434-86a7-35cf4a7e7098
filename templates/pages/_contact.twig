{% extends '_layouts/base.twig' %}

{% set mail = settings.mainEmail %}
{% set tel = settings.mainTelephone %}
{% set googleMapsUrl = settings.googleMapsUrl %}
{% set addresses = settings.mainAddress.all() %}

{% block content %}

    <div class="container grid grid-cols-1 gap-6 lg:grid-cols-12">
        <div class="col-span-full lg:col-span-7">
            {% include '_parts/content.twig' %}
        </div>
        <div class="hidden xl:block"></div>
        <div class="mt-10 col-span-full lg:mt-0 container--padding lg:col-span-5 xl:col-span-4 content">
            <section>
                <div class="flex flex-col w-full p-6 bg-primary-100 rounded-3xl">
                    <h4>{{ 'Contact details' | t }}</h4>
                    <hr class="my-6 border-secondary-300/20">
                    <div class="flex flex-wrap items-center gap-4">
                        <a href="{{ tel }}" class="inline-flex items-center pl-4 btn btn--quartenary" target="_blank">
                            {% include '_parts/icon.twig' with {
                                'icon': 'telefoon',
                                'class': 'mr-2 w-6 h-6'
                            } %}
                            <span>{{ tel.customText }}</span>
                        </a>
                        <a href="{{ mail }}" class="inline-block sm:inline-flex items-center pl-4 max-[420px]:w-full max-sm:truncate btn btn--quartenary" target="_blank">
                            {% include '_parts/icon.twig' with {
                                'icon': 'e-mail',
                                'class': 'mr-2 w-6 h-6'
                            } %}
                            <span>{{ mail.customText }}</span>
                        </a>
                    </div>
                    {% if addresses is defined and addresses is not empty %}
                        <hr class="my-6 border-secondary-300/20">
                        <div>
                            <h3 class="label">{{ 'Contact' | t }}</h3>

                            {% for address in addresses %}
                                <address class="flex flex-row py-3 not-italic lg:flex-col">
                                    {% include '_parts/icon.twig' with {
                                        'icon': address.mainAddressIcon.label,
                                        'class': 'w-6 h-6 max-lg:mt-1'
                                    } %}
                                    <div class="pl-2 lg:pl-0 p-small">
                                        {{ address.mainAddressAddress }}
                                    </div>
                                </address>
                            {% endfor %}
                        </div>
                        {% if googleMapsUrl %}
                            <div class="flex mt-2">
                                <a href="{{ googleMapsUrl }}" target="_blank" rel="noopener noreferrer" class="flex justify-between btn btn--quartenary">
                                    {{ 'Route via Google Maps' | t }}

                                    {% include '_parts/icon.twig' with {
                                        'icon': 'pijl-rechts',
                                        'class': 'ml-2 w-5 h-5 shrink-0'
                                    } %}
                                </a>
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </section>
        </div>
    </div>
{% endblock %}
