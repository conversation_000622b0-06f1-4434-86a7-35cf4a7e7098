{% set query = craft.entries.section('page').type('news').limit(12).orderBy('postDate DESC') %}

{% paginate query as pageInfo, newsEntries %}

{% extends '_layouts/base.twig' %}

{% block content %}
    <div class="container pt-8 pb-10 lg:pt-16 container--padding lg:pb-36">
        <div class="
                w-full grid gap-2
                sm:grid-cols-[repeat(auto-fill,minmax(26rem,1fr))]
            ">
                {% for entry in newsEntries %}
                    {% include '_parts/news/_card' with {
                        entry
                    } only %}
                {% endfor %}
            </div>
            {% include '_parts/pagination' with {
                pageInfo,
                paginationLabel: 'News pagination' | t,
                paginationClass: 'mt-10'
            } %}
    </div>
{% endblock %}