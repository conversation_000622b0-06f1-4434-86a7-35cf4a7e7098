{% extends "_layouts/base.twig" %}

{% set code = 404 %}
{% set entry = [] %}

{% block keyvisual %}
    {% include '_parts/breadcrumbs.twig' with {
        code,
        entry
    } %}
    <div class="flex w-full h-12 lg:h-20 bg-secondary lg:bg-gradient-to-r from-[#00325F] -from-0.34% via-secondary-500 via-40.81% to-primary-500 to-99.66%"></div>
{% endblock %}

{% block content %}
    <div class="container content">
        <section class="container flex flex-col items-center gap-10 md:gap-5 container--padding">
            <div class="w-full max-w-4xl wysiwyg">
                <h1>{{ 'Page not found' | t }}</h1>

                <p>{{ 'Sorry, we could not find the page you were looking for.' | t }}</p>
            </div>
        </section>
    </div>
    {% include '_parts/content.twig' with {
        blocks: fourOFour.contentBlocks.all()
    } %}
{% endblock %}
