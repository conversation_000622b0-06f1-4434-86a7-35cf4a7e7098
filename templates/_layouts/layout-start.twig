<!DOCTYPE html>
<html lang="{{ currentSite.language }}" dir="{{ craft.app.i18n.getLocaleById(currentSite.id).getOrientation() }}" class="motion-safe:scroll-smooth">
<head>
    <meta charset="{{ _charset }}"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta name="application-name" content="{{ siteName }}"/>

    <link rel="icon" sizes="any" href="/favicon.ico">
    <link rel="icon" type="image/svg+xml" href="/dist/images/favicons/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="/dist/images/favicons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/dist/images/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/dist/images/favicons/favicon-16x16.png">
    <link rel="manifest" href="/dist/images/favicons/site.webmanifest">
    <link rel="mask-icon" href="/dist/images/favicons/safari-pinned-tab.svg" color="#11355e">
    <meta name="msapplication-TileColor" content="#11355e">
    <meta name="theme-color" content="#ffffff">

    {% set env = getenv('CRAFT_ENVIRONMENT') %}
    {% if env == 'production' %}
        <script type="text/javascript">
            (function(window, document, dataLayerName, id) {
            window[dataLayerName]=window[dataLayerName]||[],window[dataLayerName].push({start:(new Date).getTime(),event:"stg.start"});var scripts=document.getElementsByTagName('script')[0],tags=document.createElement('script');
            function stgCreateCookie(a,b,c){var d="";if(c){var e=new Date;e.setTime(e.getTime()+24*c*60*60*1e3),d="; expires="+e.toUTCString();f="; SameSite=Strict"}document.cookie=a+"="+b+d+f+"; path=/"}
            var isStgDebug=(window.location.href.match("stg_debug")||document.cookie.match("stg_debug"))&&!window.location.href.match("stg_disable_debug");stgCreateCookie("stg_debug",isStgDebug?1:"",isStgDebug?14:-1);
            var qP=[];dataLayerName!=="dataLayer"&&qP.push("data_layer_name="+dataLayerName),isStgDebug&&qP.push("stg_debug");var qPString=qP.length>0?("?"+qP.join("&")):"";
            tags.async=!0,tags.src="https://waarderingskamer.containers.piwik.pro/"+id+".js"+qPString,scripts.parentNode.insertBefore(tags,scripts);
            !function(a,n,i){a[n]=a[n]||{};for(var c=0;c<i.length;c++)!function(i){a[n][i]=a[n][i]||{},a[n][i].api=a[n][i].api||function(){var a=[].slice.call(arguments,0);"string"==typeof a[0]&&window[dataLayerName].push({event:n+"."+i+":"+a[0],parameters:[].slice.call(arguments,1)})}}(i[c])}(window,"ppms",["tm","cm"]);
            })(window, document, 'dataLayer', '46db3e37-a9d4-4517-b990-ee54834ef9ff');
        </script>
    {% endif %}

    {% include "_parts/base/font-preloading" %}
    {{ head() }}

    {{ craft.vite.script('src/js/app.ts', false) }}

    {% block title %}
        {% if craft.app.request.pathInfo == 'search' %}
            <title>{{ 'Search Results in the Waarderingskamer' | t }}</title>
        {% endif %}
    {% endblock %}
</head>
    {% if entry is defined %}
        {% apply spaceless %}
            {{ adminBar({entry: entry}) }}
        {% endapply %}
    {% endif %}

    {% block bodyTag %}
        <body class="{{ entry is defined ? entry.section.handle | kebab : '' }} relative flex flex-col min-h-screenh antialiased {{ body_class | default('') }}">
    {% endblock %}
    <div id="overlay"></div>
