{% macro tableRow(name, period, value, valueType) %}
    {% set locale = craft.app.i18n.getLocaleById(currentSite.language | slice(0, 2)) %}

    <li class="flex flex-row max-lg:flex-col w-full justify-between">
        <span class="bg-secondary-100 max-lg:w-full w-4/5 max-lg:mb-1 mr-2 max-lg:p-4 p-6 rounded-lg">
            {{ name }}
            {% if period %}
                <br /> <span class="text-[0.800rem]">{{ period }}</span>
            {% endif %}
        </span>
        <span class="bg-secondary-100 max-lg:w-full w-1/5 max-lg:p-4 p-6 rounded-lg lg:justify-end flex font-semibold items-center">
            {% if valueType == 'percentage' %}
                {{ (value / 100) | format_percent_number({fraction_digit: 1}, locale=locale) }}
            {% elseif valueType == 'money' %}
                {{ value | format_currency('EUR', {fraction_digit: 0}, locale=locale) }}
            {% elseif valueType == 'date' %}
                {{ value | date('d-m-Y') }}
            {% elseif valueType == 'default' %}
                {{ value | number_format(0, ',', '.') }}
            {% else %}
                {{ value }}
            {% endif %}
        </span>
    </li>
{% endmacro %}

<section id="woz-information" class="flex flex-col gap-8 scroll-mt-20 max-lg:scroll-mt-44">

    <ul class="grid lg:gap-2 gap-6">
        {% for information in organization.information %}
            {% switch information.presentation %}
                {% case "perc" %}
                    {{ _self.tableRow(information.title, information.period, information.value, 'percentage') }}
                {% case "euro" %}
                    {% if information.code == "GWWAWK" %}
                        {{ _self.tableRow(information.title, information.period, (information.value * 1000), 'money') }}
                    {% else %}
                        {{ _self.tableRow(information.title, information.period, (information.value), 'money') }}
                    {% endif %}
                {% case "default" %}
                    {{ _self.tableRow(information.title, information.period, information.value, 'default') }}

                {% default %}
                    {{ _self.tableRow(information.title, information.period, information.value) }}

            {% endswitch %}
        {% endfor %}
    </ul>

    <div class="grid gap-6">
        {% for statistic in organization.statistics %}
            <div class="flex flex-col justify-between gap-6 p-6 bg-secondary-100 rounded-2xl">
                <div class="flex flex-row justify-between items-center gap-4">
                    <div>
                        <h5>{{ statistic.title }}</h5>
                        {% if statistic.currentYear %}
                            <div class="flex flex-row gap-4">
                                <span class="w-[58px]">{{ statistic.currentYear.label | slice(0, 4) }}</span>
                                <span>({{ 'reference date' | t }} {{ statistic.currentYear.date | date("d-m-Y") }})</span>
                            </div>
                        {% endif %}
                    </div>

                    {% if statistic.currentYear %}
                        {% switch statistic.currentYear.presentation %}
                            {% case "perc" %}
                                <span class="h2">{{ statistic.currentYear.value | number_format(1, ',', '.') }}%</span>

                            {% case "euro" %}
                                <span class="h2">&euro;{{ statistic.currentYear.value | number_format(0, ',', '.') }}</span>

                            {% default %}
                                <span class="h2">{{ statistic.currentYear.value | number_format(1, ',', '.') }}</span>

                        {% endswitch %}
                    {% endif %}
                </div>

                <hr class="h-px bg-secondary-300/20 border-0">

                <button aria-expanded="false" aria-controls="statistic-{{ loop.index }}"
                    class="group text-secondary-500 flex text-left peer transition-all
                        hover:font-bold hover:text-primary-700
                        focus-visible:font-bold focus-visible:text-primary-700
                    ">
                    <span class=" hidden group-aria-expanded:block">{{ 'Show less' | t }}</span>
                    <span class="block group-aria-expanded:hidden">{{ 'Show more' | t }}</span>
                    {% include "_parts/icon" with {
                        icon: 'chevron-omlaag',
                        class: 'w-6 h-6 ml-2 group-aria-expanded:rotate-180'
                    } %}
                </button>

                <div id="statistic-{{ loop.index }}" class="statistic-content grid-cols-1 divide-y divide-secondary-300/20 peer-aria-expanded:grid hidden transition-all">
                    {% for year in statistic.history %}
                        <div class="py-4 first:pt-0 last:pb-0 flex flex-row justify-between">
                            <div class="flex flex-row gap-4">
                                <span class="w-[58px]">{{ year.label[:4] }}</span>
                                <span>
                                    {{ year.label[5:] | split('[')[0] }}
                                </span>
                            </div>

                            <span class="font-bold">
                                {% switch year.presentation %}
                                    {% case "perc" %}
                                        {{ year.value | number_format(1, ',', '.') }}%

                                    {% case "euro" %}
                                        &euro;{{ year.value | number_format(0, ',', '.') }}

                                    {% default %}
                                        {{ year.value | number_format(1, ',', '.') }}
                                {% endswitch %}
                            </span>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endfor %}
    </div>
</section>
