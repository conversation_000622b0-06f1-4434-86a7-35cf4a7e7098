{% block breadcrumb %}

    {% if entry is defined %}

        {% set nonElementLinks = false %}
        {% set breadcrumbLinks = [] %}
        {# home #}
        {% set home = craft.app.getElements().getElementByUri('__home__', currentSite.id) %}
        {% set breadcrumbLinks = breadcrumbLinks | merge([{
            url: home.url ?? alias(currentSite.baseUrl),
            title: home.title ?? 'homepage' | t,
        }]) %}

        {# get elements #}

        {% if not code | default() %}
            {% set segments = craft.app.request.segments %}
            {% for segment in segments %}
                {% set uriPart = segments[0:loop.index] | join('/') | literal %}
                {% set element = craft.app.elements.getElementByUri(uriPart, currentSite.id) %}
                {% if element %}
                    {% set breadcrumbLinks = breadcrumbLinks | merge([{
                        url: element.url,
                        title: element.title,
                    }]) %}
                {% elseif nonElementLinks %}
                    {% set breadcrumbLinks = breadcrumbLinks | merge([{
                        url: url(uriPart),
                        title: segment | t,
                    }]) %}
                {% endif %}
            {% endfor %}
        {% else %}
            {% set breadcrumbLinks = breadcrumbLinks | merge([{
                url: '#',
                title: code,
            }]) %}
        {% endif %}

        {% set textColor = entry.keyvisualType == 'withoutKeyvisual' ? "text-secondary-300 focus:text-secondary-300" : "text-primary-100 focus:text-secondary-100" %}

        {% if breadcrumbLinks | length > 1 %}
            <nav class="breadcrumb {{ textColor }} bg-secondary-500 md:bg-none">
                <div class="container container--padding">
                    <div class="relative">
                        <ol class="relative md:absolute z-10 flex flex-row flex-wrap gap-y-2 lg:pt-0 lg:mt-8 pt-8 mt-0 isolate lg:w-3/5">
                            {% for breadcrumb in breadcrumbLinks %}
                                <li class="after:content-['/'] after:mx-2{{ loop.last ? ' after:hidden' : '' }}">
                                    {% if loop.last == false %}
                                        <a class="no-underline transition-colors underline-offset-1 decoration-from-font {{ textColor }} hover:underline" href="{{ breadcrumb.url }}">
                                    {% else %}
                                        <span aria-current="page">
                                    {% endif %}
                                    <span>
                                        {{ breadcrumb.title == 'Home' ? 'Homepage' : breadcrumb.title | raw }}
                                    </span>
                                    {% if loop.last == false %}
                                        </a>
                                    {% else %}
                                        </span>
                                    {% endif %}
                                </li>
                            {% endfor %}
                        </ol>
                    </div>
                </div>
            </nav>
        {% endif %}

    {% endif %}
{% endblock %}
