{% set image = entry.doorwayImage.one | default(null) %}

{% set categories = entry.categories.select(['title']).all %}

<article class="
    swiper-slide
    group overflow-hidden transition-colors relative flex flex-col min-h-[420px] h-auto items-center bg-secondary-100 rounded-3xl
">
    <div class="relative overflow-hidden w-full">
        {% if image %}
            {% include '_parts/picture.twig' with {
                image,
                width: 432,
                height: 190,
                class: 'w-full h-[190px] object-cover transition-transform rounded-br-shape group-hover:-translate-y-[10px] group-hover:scale-105 transition-all'
            } %}
        {% else %}
            <div class="aspect-[432/187] w-full"></div>
        {% endif %}
        {% if categories is defined %}
            {% include '_parts/news/categories' with {
                categories,
                categoriesClass: 'absolute top-0 left-0 p-4'
            } only %}
        {% endif %}
    </div>
    <div class="relative p-4 grow flex flex-col w-full gap-4 justify-between">
        <div class="flex flex-col gap-2">
            <h4 class="text-lg">{{ entry.title }}</h4>
            {% if entry.doorwayText %}
                <span>{{ entry.doorwayText }}</span>
            {% endif %}
        </div>
        <div class="flex justify-between items-center">
            <time datetime="{{ entry.postDate | date('d-m-Y') }}" class="text-sm">{{ entry.postDate | date('d-m-Y') }}</time>
            <a href="{{ entry.url }}" class="btn btn--secondary inline-block items-center flex gap-4" aria-label="{{ entry.title }}">
            {{ 'View' | t }}
            {% include '_parts/icon' with {
                icon: 'pijl-rechts',
                class: 'h-6 w-6'
            } %}
            </a>
        </div>
    </div>
    <a href="{{ entry.url }}" class="absolute inset-0 size-full rounded-lg" aria-hidden="true" tabindex="-1">
        <span class="sr-only">{{ entry.title }}</span>
    </a>
</article>
