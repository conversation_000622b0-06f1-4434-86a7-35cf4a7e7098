<li class="relative grid w-full md:grid-cols-4 group bg-primary-100 rounded-xl">
    <div class="flex flex-col px-6 py-4 md:col-span-3">
        <span class="pb-1 label hyphens-manual">{{ title }}</span>
        <span class="text-secondary-500">
            {{ 'Publication date:' | t }} <time datetime="{{ publicationDate | date("Y-m-d") }}">{{ publicationDate | format_datetime('long', 'none', locale=locale) }}</time>
        </span>
    </div>
    <a href="{{ url }}" target="_blank" rel="noopener" class="flex items-center px-6 py-4 no-underline transition-all border-t md:col-span-1 md:border-t-0 md:border-l border-secondary-500 text-secondary-500 group-hover:rounded-b-xl group-hover:bg-secondary-500 group-hover:text-white md:group-hover:rounded-b-none md:group-hover:rounded-r-xl hover:cursor-pointer after:absolute after:inset-0"
        {% if ext == 'csv' %}download="{{ title }}.csv">
            {% include '_parts/icon.twig' with {
                'icon': 'downloaden',
                'class': 'w-5 h-5 mr-[1.125rem]'
            } %}

            {{ 'Download {extension}' | t(params={extension: (ext ?? 'PDF') | upper}) }}
        {% else %}
            >
            {% include '_parts/icon.twig' with {
                'icon': 'pijl-omhoog-rechts',
                'class': 'w-5 h-5 mr-[1.125rem]'
            } %}

            {{ 'View {extension}' | t(params={extension: (ext ?? 'PDF') | upper}) }}
        {% endif %}

        <span class="sr-only">{{ title }}</span>
    </a>
</li>
