{# facebook #}
{% set facebookSameAs = seomatic.site.sameAsLinks["facebook"] is defined ? seomatic.site.sameAsLinks["facebook"] %}
{% if facebookSameAs %}
    {% set facebook = {
        'label': facebookSameAs["siteName"],
        'url': facebookSameAs["url"],
        'icon': 'facebook'
    } %}
{% endif %}

{# twitter #}
{% set twitterSameAs = seomatic.site.sameAsLinks["twitter"] is defined ? seomatic.site.sameAsLinks["twitter"] %}
{% if twitterSameAs %}
    {% set twitter = {
        'label': twitterSameAs["siteName"],
        'url': twitterSameAs["url"],
        'icon': 'twitter'
    } %}
{% endif %}

{# linkedin #}
{% set linkedinSameAs = seomatic.site.sameAsLinks["linkedin"] is defined ? seomatic.site.sameAsLinks["linkedin"] %}
{% if linkedinSameAs %}
    {% set linkedin = {
        'label': linkedinSameAs["siteName"],
        'url': linkedinSameAs["url"],
        'icon': 'linkedIn'
    } %}
{% endif %}

{# if there are any socials #}
{% if facebookSameAs or twitterSameAs or linkedinSameAs %}
    <section aria-labelledby="footer-heading-socials">
        <h3 id="footer-heading-socials" class="text-white h4">{{ 'Social media' | t }}</h3>

            {# macro for item #}
            {% macro socialItem(obj) %}
             <li class="pr-4 list-none inline-block">
                <a href="{{ obj.url }}" class="group inline-flex w-6 h-6 items-center justify-center text-inherit hover:text-inherit hover:text-primary-100 focus-visible:text-primary-100 text-white" target="_blank" rel="noopener noreferrer">
                    {% include '_parts/icon.twig' with {
                        icon: obj.icon,
                        class: 'h-[1.75em] transition-transform group-hover:scale-110'
                    } %}
                    <span class="sr-only">{{ obj.label }}</span>
                </a>
            </li>
            {% endmacro %}

        <ul class="flex flex-row pt-6 lg:pt-10">
            {# try to render each social channel #}
            {{ facebookSameAs ? _self.socialItem(facebook) }}
            {{ twitterSameAs ? _self.socialItem(twitter) }}
            {{ linkedinSameAs ? _self.socialItem(linkedin) }}
        </ul>
    </section>
{% endif %}
