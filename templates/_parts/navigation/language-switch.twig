{% set langSwitcher = craft.app.sites.getAllSites() %}

<nav class="ml-4" aria-label="{{ 'Choose language' | t }}">
    <ul class="flex flex-row items-center">
        {% for lang in langSwitcher %}
            {# Set homepage as default #}
            {% set url = lang.getBaseUrl() %}
            {% set locale = craft.app.i18n.getLocaleById(lang.language | slice(0, 2)) %}

            {# Entry is defined #}
            {% if entry is defined %}
                {# Check if that entry exists in other locale #}
                {% set localeEntry = craft.entries.siteId(lang.id).id(entry.id).one() %}
                {% if localeEntry %}
                    {% set url = localeEntry.url %}
                {% endif %}
            {# Category is defined #}
            {% elseif category is defined %}
                {# Check if that entry exists in other locale #}
                {% set localeCategory = craft.categories.siteId(lang.id).id(category.id).one() %}
                {% if localeCategory %}
                    {% set url = localeCategory.url %}
                {% endif %}
            {% endif %}

            <li class="px-3 py-1 nav-language-item">
                <a href="{{ url }}" class="nav-language-item-link{{ lang.id == currentSite.id ? ' nav-language-item-link--active' }}" lang="{{ locale }}" hreflang="{{ locale }}" aria-label="{{ lang.name }}">
                    {{ locale | upper }}
                </a>
            </li>
        {% endfor %}
    </ul>
</nav>
