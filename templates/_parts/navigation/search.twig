<form id="nav-search" action="{{ currentSite.baseUrl }}search" method="get" itemscope itemtype="https://schema.org/WebSite">
    <meta itemprop="url" content="{{ craft.app.request.absoluteUrl }}" />
    <div class="flex flex-row relative" itemprop="potentialAction" itemscope itemtype="https://schema.org/SearchAction">
        <label for="{{ id }}" class="sr-only">{{ label | default(label | default('Search' | t)) }}</label>
        <meta itemprop="target" content="{{ craft.app.request.absoluteUrl }}?q={query}" />
        {{ input('search', 'query', '', {
            id: id,
            class: "placeholder-black placeholder-opacity-70 w-full border-secondary border rounded-2xl text-black text-opacity-70 " ~ class | default('py-3 pl-4 pr-10'),
            placeholder: label | default('Search' | t),
            itemprop: "query-input"
        }) }}
        <button type="submit" class="p-4 pr-5 absolute right-0 top-1/2 -translate-y-1/2">
            <span class="sr-only">{{ label | default(label | default('Search' | t)) }}</span>
            <span>
                {% include '_parts/icon.twig' with {
                    'icon': 'zoeken',
                    'class': 'w-5 h-5 text-secondary-500'
                } %}
            </span>
        </button>
    </div>
</form>
