{% if sizes is not defined %}
    {% set sizes = ['0.25x', '0.5x', '1x'] %}
{% endif %}

{% set resize = {
    mode: mode is defined ? mode : 'crop',
    width: width is defined ? width : 0,
    height: height is defined ? height : 0,
    quality: quality is defined ? quality : 80,
} %}

{% if image.extension != 'svg' or format is defined %}
    {% set resize = resize | merge({
        format: format is defined ? format : 'webp'
    }) %}
{% endif %}

{% if image.extension != 'svg' %}
    {% set imageUrlsBySizes = image.getUrlsBySize(sizes, resize) | map((url, size) => "#{url} #{size}") | join(', ') %}
    {% set imageUrl = image.getUrl(resize) %}
{% else %}
    {% set imageUrl = image.url %}
{% endif %}

{% set width = width ?? image.getWidth() %}
{% set height = height ?? image.getHeight() %}

<picture>
    {% if image.extension != 'svg' or image.extension != 'gif' %}
        <source type="image/webp" srcset="{{ image.getUrlsBySize(sizes, resize | merge({format: 'webp'})) | map((url, size) => "#{url} #{size}") | join(', ') }}">
    {% endif %}

    {% include "_parts/image" %}
</picture>
