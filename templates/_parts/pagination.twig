{#  Usage:
    {% set query = craft.entries.section('sectionHandle').limit(PER_PAGE_LIMIT) %}
    {% paginate query as pageInfo, entries %}

    Add your entries loop here
    
    {% embed '_parts/pagination' with {
        pageInfo,
        paginationLabel: 'Section name pagination' | t,
        paginationClass: ''
    } %}
        {% block beforePagination %}
            <div class="mt-16">
        {% endblock %}
        {% block afterPagination %}
            </div>
        {% endblock %}
    {% endembed %}
#}
{% set prevNextButtonClass = '
    group flex items-center gap-2 h-10 no-underline
    disabled:opacity-50 disabled:pointer-events-none
' %}
{% set pageButtonClass = '
    flex items-center justify-center border border-secondary-500 text-secondary-500 transition-colors w-10 h-10 no-underline
    hover:text-white hover:bg-secondary-500
    focus-visible:text-white focus-visible:bg-secondary-500
    [&.active]:text-white [&.active]:bg-secondary-500
' %}

{% if pageInfo.totalPages > 1 %}
    {% block beforePagination %}{% endblock %}
    <nav
        aria-label="{{ paginationLabel }}"
        class="
            flex justify-center items-center gap-3 text-sm
            lg:gap-4 lg:text-lg
            {{ paginationClass | default('') }}
        "
    >
        {% set prevUrl = pageInfo.getPrevUrl() %}
        <{{ prevUrl ? 'a' : 'button' }}
            class="{{ prevNextButtonClass }}"
            {% if prevUrl %}
                href="{{ prevUrl }}"
            {% else %}
                disabled
            {% endif %}
        >
            {% include '_parts/icon' with {
                icon: 'chevron-links',
                class: 'text-secondary h-5 group-hover:text-secondary-400 group-disabled:text-black'
            } %}
            {{ 'Previous' | t }}
        </{{ prevUrl ? 'a' : 'button' }}>

        {% for pageNumber, url in pageInfo.getDynamicRangeUrls(3) %}
            {% set ariaLabel = 'Page {page}' | t(params={page: pageNumber}) %}
            {% set isActivePage = pageNumber == pageInfo.currentPage %}
            <a
                class="
                    {{ pageButtonClass }}
                    {{ isActivePage ? 'active' }}
                "
                {% if isActivePage %}
                    aria-current="page"
                {% else %}
                    href="{{ url }}"
                {% endif %}
                aria-label="{{ ariaLabel }}"
            >
                {{ pageNumber }}
            </a>
        {% endfor %}

        {% set nextUrl = pageInfo.getNextUrl() %}
        <{{ nextUrl ? 'a' : 'button' }}
            class="{{ prevNextButtonClass }}"
            {% if nextUrl %}
                href="{{ pageInfo.getNextUrl() }}"
            {% else %}
                disabled
            {% endif %}
        >
            {{ 'Next' | t }}
            {% include '_parts/icon' with {
                icon: 'chevron-rechts',
                class: 'text-secondary h-5 group-hover:text-secondary-400 group-disabled:text-black'
            } %}
        </{{ nextUrl ? 'a' : 'button' }}>
    </nav>
    {% block afterPagination %}{% endblock %}
{% endif %}
