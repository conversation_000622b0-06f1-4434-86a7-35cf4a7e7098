{% set title = entry.heading %}
{% set wysiwyg = entry.wysiwyg %}
{% set buttons = entry.keyvisualButtons %}
{% set image = entry.image.one() | default() %}

<header class="bg-secondary-500 text-white relative flex md:flex-row flex-col justify-between isolate overflow-hidden min-h-[32rem]">
    <div class="container container--padding pb-24 z-5 flex items-center
        {% if entry.isHomepage %}
            pt-10 md:pt-[5.5rem]
        {% else %}
            pt-16 md:pt-[7.5rem]
        {% endif %}
    ">
        <div class="max-w-none md:max-w-[50vw] 2xl:max-w-[52vw];">
            {% if title is defined and title is not empty %}
                <h1 class="text-white">{{ title }}</h1>
            {% endif %}

            {% if wysiwyg is defined and wysiwyg is not empty %}
                <div class="wysiwyg pt-6 [&>*]:text-white">{{ wysiwyg }}</div>
            {% endif %}

            {% if buttons is defined and buttons is not empty %}
                <div class="flex flex-wrap gap-4 pb-2 pt-8 xl:pt-16">
                    {% for button in buttons %}
                        {{ button.button.getLink({
                            class: 'btn btn--tertiary inline-block items-center flex'
                        }) }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>

    {% if image is defined and image is not empty %}
        <div class="flex items-center justify-end h-full md:absolute md:top-0 md:right-0 md:w-[40vw] isolate">
            <div class="keyvisual--gradient aspect-1 flex justify-end">
                {% include "_parts/picture.twig" with {
                    loading: 'eager',
                    class: 'block w-full h-full leaf-shape relative z-10 lg:max-w-[560px]',
                    image
                } %}
            </div>
        </div>
    {% endif %}
</header>