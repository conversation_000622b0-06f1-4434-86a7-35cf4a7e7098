{% set title = entry.heading %}
{% set buttons = entry.keyvisualButtons %}
{% set image = entry.image.one() | default() %}

<header class="relative pb-40 md:pb-0 isolate overflow-hidden">
    {% include "_parts/picture.twig" with {
        loading: 'eager',
        class: 'w-full md:w-4/5 h-full object-cover absolute top-0 right-0',
        image
    } %}
    <div class="z-10 md:absolute inset-0 max-w-none min-[3450px]:max-w-[43%] bg-gradient-to-r min-[3450px]:bg-secondary-500 from-[28vw] from-secondary-500 via-transparent via-[28vw] to-transparent"></div>
    <div class="relative z-20 container md:container--padding">
        <div class="keyvisual--content md:max-w-3xl max-w-none bg-secondary-500 py-14 md:py-32 lg:pr-20 px-6 md:pl-0 pr-24">
            {% if title is defined and title is not empty %}
                <h1 class="text-white py-6 z-20">{{ title }}</h1>
            {% endif %}

            {% if buttons is defined and buttons is not empty %}
                <div class="flex flex-row flex-wrap gap-4 py-2 pr-20 z-20">
                    {% for button in buttons %}
                        {{ button.button.getLink({
                            class: 'btn btn--tertiary inline-block items-center flex'
                        }) }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</header>
