{% set title = organization.organizationName %}

<header class="relative flex flex-col justify-between overflow-hidden text-white bg-secondary-500 lg:flex-row isolate">
    <div class="container pt-16 pb-24 container--padding z-5 md:pt-20">

        <div class="grid gap-4 grid-areas-slim lg:grid-areas-wide lg:grid-cols-wide">

            <div class="flex flex-col gap-4 items-start grid-in-title">
                 {% if organization.logo %}
                    <img src="{{ organization.logo }}" class="object-contain h-40 w-40" alt />
                {% endif %}

                {% if title is defined and title is not empty %}
                    <h1 class="text-white">{{ title }}</h1>
                {% endif %}
            </div>

            <div class="flex flex-wrap items-end gap-4 grid-in-links">
                    <a href="{{ organization.website }}" class="inline-flex items-center font-medium btn btn--tertiary" target="_blank">
                        {% include '_parts/icon.twig' with {
                            'icon': 'pijl-omhoog-rechts',
                            'class': 'w-6 h-6 mr-2 shrink-0'
                        } %}
                        {{ 'Go to municipality' | t }}
                    </a>
                    <a href="https://mijn.overheid.nl/" target="_blank" rel="noopener noreferrer" class="inline-flex items-center font-medium btn btn--tertiary">
                        {% include '_parts/icon.twig' with {
                            'icon': 'www',
                            'class': 'w-6 h-6 mr-2 shrink-0'
                        } %}
                        {{ 'Mijn.overheid.nl' | t }}
                    </a>
            </div>

            <div class="flex flex-col p-6 bg-white grid-in-info rounded-xl">
                <h4>{{ 'Judgement' | t }} {{ organization.title }}</h4>

                <div class="flex items-center gap-6 mt-4">
                    <div class="flex h-6 gap-2">
                        {% for i in 1..5 %}
                            {% set icon = i <= organization.rating ? 'star-filled' : 'star' %}

                            {% if i == organization.rating | round(0, 'floor') + 1 and organization.rating | round(0, 'floor') != organization.rating %}
                                {% set icon = 'star-half-filled' %}
                            {% endif %}

                            {% set iconOpacity = icon == 'star' ? ' opacity-40' %}
                            {% include "_parts/icon.twig" with {
                                'icon': icon,
                                'class': 'w-6 h-6 text-primary-700 fill-none' ~ iconOpacity
                            } %}
                        {% endfor %}
                    </div>
                    <span class="leading-6 label">
                        {% if organization.rating >= 4 %}
                            {{ 'Good' | t }}
                        {% elseif organization.rating >= 3 %}
                            {{ 'Sufficient' | t }}
                        {% elseif organization.rating >= 2 %}
                            {{ 'Must be improved in some areas' | t }}
                        {% else %}
                            {{ 'Insufficient' | t }}
                        {% endif %}
                    </span>

                </div>

                <p class="p-2 my-10 rounded-2xl bg-secondary-100">
                    {{ organization.statusConsent }}
                </p>

                {% if settings.assessmentExplanationLink %}
                        <a {{ settings.assessmentExplanationLink.getLinkAttributes() }} class="inline-flex items-center justify-center w-full font-medium btn btn--primary md:w-auto">
                            {{ settings.assessmentExplanationLink.getCustomText('How we assess' | t) }}

                            {% include "_parts/icon.twig" with {
                                'icon': 'pijl-omhoog-rechts',
                                'class': 'w-6 h-6 text-white'
                            } %}
                        </a>
                {% endif %}
            </div>
        </div>
    </div>
</header>
