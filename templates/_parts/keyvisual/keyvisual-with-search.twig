{% set title = entry.heading %}
{% set frequentlySearched = entry.keyvisualFrequentlySearchedButtons %}
{% set image = entry.image.one() | default() %}

<header class="bg-secondary-500 text-white relative flex lg:flex-row flex-col justify-between isolate overflow-hidden">
    <div class="container container--padding pb-24 z-5
        {% if entry.isHomepage %}
            pt-10 md:pt-[4.5rem]
        {% else %}
            pt-16 md:pt-[7.5rem]
        {% endif %}
    ">
        <div class="lg:max-w-md xl:max-w-xl 2xl:max-w-3xl max-w-none">
            {% if title is defined and title is not empty %}
                <h1 class="text-white pb-6 lg:pb-10">{{ title }}</h1>
            {% endif %}

            <div class="py-4">
                <h3 class="text-white h5 pb-4">{{ "We're here to help" | t }}</h3>

                <div class="w-full">
                    {% include "_parts/navigation/search" with {
                        id: 'search-keyvisual',
                        label: 'Start searching' | t,
                        class: 'py-5 pl-4 pr-10'
                    } only %}
                </div>
            </div>

            {% if frequentlySearched is defined and frequentlySearched is not empty %}
                <div class="py-4 flex flex-col xl:flex-row gap-2 lg:gap-4 ">
                    <h4 class="text-white label inline-block flex-shrink-0 py-2">{{ 'Often searched:' | t }}</h4>

                    <ul class="flex flex-row flex-wrap gap-2 lg:gap-4">
                        {% for item in frequentlySearched %}
                            <li>
                                {{ item.button.getLink({
                                    class: 'btn btn--tertiary border-primary-500 inline-block py-2 px-4'
                                }) }}
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
        </div>
    </div>

    {% if image is defined and image is not empty %}
        <div class="flex items-center xl:items-end justify-end h-full lg:absolute lg:top-0 lg:right-0 lg:w-[40vw] isolate">
            <div class="keyvisual--gradient lg:h-2/3 xl:h-[91%] aspect-1">
                {% include "_parts/picture.twig" with {
                    loading: 'eager',
                    class: 'block w-full h-full leaf-shape relative z-10',
                    image
                } %}
            </div>
        </div>
    {% endif %}
</header>
