{% set blocks = blocks | default(entry.contentBlocks.all()) %}

<div class="container content">
    {% if blocks | length %}
        {% for block in blocks %}
            {# twigcs use-var block #}
            {% if block.quickLinkTitle is not empty %}
                <a class="quick-links__anchor scroll-mt-20 max-lg:scroll-mt-44" id="{{ block.quickLinkTitle | kebab }}"></a>
            {% endif %}
            {% include '_blocks/' ~ block.type | kebab ~ '.twig' with {
                'block': block
            } %}
        {% endfor %}
    {% endif %}
</div>
