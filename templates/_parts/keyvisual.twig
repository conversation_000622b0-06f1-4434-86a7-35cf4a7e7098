{% if entry is defined %}
    {% set type = entry.keyvisualType | default() %}
    {% if type != 'withoutKeyvisual' and organization is not defined %}
        {% set doorwayLink = entry.keyvisualDoorwayLink.one() | default() %}
        <div class="{{ doorwayLink ? 'mb-20' }}">
            {% if type == 'keyvisualWithSearch' %}
                {% include "_parts/keyvisual/keyvisual-with-search.twig" %}
            {% elseif type == 'keyvisualWithButtons' %}
                {% include "_parts/keyvisual/keyvisual-with-buttons.twig" %}
            {% else %}
                {% include "_parts/keyvisual/keyvisual-with-text.twig" %}
            {% endif %}


            {% if doorwayLink %}
                {% include "_parts/keyvisual/keyvisual-doorway-link.twig" with {item: doorwayLink} only %}
            {% endif %}
        </div>
    {% elseif organization is defined %}
        <div class="mb-20">
            {% include "_parts/keyvisual/keyvisual-organization.twig" %}
        </div>
    {% else %}
        <div class="pt-20"></div>
    {% endif %}

{% endif %}
