{% set municipalPortal = settings.municipalPortal %}
{% set menus = settings.mainNavMenu.with(['submenu', 'menuItem']).all() %}
{% set topMenuItemClass = 'block px-4 py-6 no-underline peer text-current underline-offset-8 hover:underline hover:text-current focus-visible:text-current' %}

<div
    x-data="{ open: false }"
    x-trap.inert="open"
    class="relative top-0 z-50 bg-white max-lg:sticky"
>
    <div id="nav-desktop" class="flex flex-col" aria-label="Menu">
        <div class="bg-secondary font-medium text-sm">
            <div class="container flex flex-row justify-end container--padding">
                <a href="{{ municipalPortal }}" class="flex flex-row items-center p-2 mr-4 no-underline max-xl:hidden">
                    {% include '_parts/icon.twig' with {
                        'icon': 'gebruiker',
                        'class': 'mr-2 w-5 h-5 text-white'
                    } %}
                    <span class="text-white">{{ municipalPortal.customText }}</span>
                </a>

                {% include "_parts/navigation/language-switch.twig" %}
            </div>
        </div>

        <div class="bg-white">
            <div class="container flex flex-row items-center justify-between max-xl:hidden container--padding">
                <div class="flex flex-row items-center">
                    <a href="{{ siteUrl }}" aria-label="{{ 'Back to home' | t }}">
                        <img src="{{ craft.vite.asset("/images/logo-basic.svg", true) }}" alt="{{ 'Logo of the Waarderingskamer' | t }}" width="50" height="50" class="hidden max-2xl:block">
                        <img src="{{ craft.vite.asset("/images/logo.svg", true) }}" alt="{{ 'Logo of the Waarderingskamer' | t }}" width="200" height="50" class="block py-2 pr-2 max-2xl:hidden">
                    </a>

                    <nav class="font-medium">
                        <ul class="flex flex-row items-center">
                            {% for menu in menus %}
                                {% switch menu.type.handle %}
                                    {% case "submenu" %}
                                        {% set submenu = menu.mainNavSubmenu.with(['mainNavSubmenuItemLink']).all() %}
                                        <li class="relative group text-secondary-500">
                                            <button
                                                class="{{ topMenuItemClass }}"
                                                id="has-submenu-{{ random() }}"
                                                aria-controls="submenu-{{ menu.id }}"
                                                aria-expanded="false"
                                            >
                                                {{ menu.navLabel }}

                                                {% include '_parts/icon.twig' with {
                                                    'icon': 'chevron-omlaag',
                                                    'class': 'w-5 h-5'
                                                } %}
                                            </button>

                                            {% if submenu %}
                                                <ul id="submenu-{{ menu.id }}"
                                                    class="
                                                        absolute invisible bg-white drop-shadow-2xl transition-all opacity-0 top-20 rounded-2xl w-max max-w-[300px] -translate-y-2
                                                        peer-aria-expanded:visible peer-aria-expanded:opacity-100 peer-aria-expanded:translate-y-0
                                                        before:content-[''] before:-top-4 before:h-4 before:absolute before:w-full z-50 py-3 ">
                                                    {% for item in submenu %}
                                                        <li class="font-medium">
                                                            {{ item.mainNavSubmenuItemLink.getLink({
                                                                class: 'px-6 py-3 min-w-[12rem] hyphens-auto block text-current no-underline underline-offset-8 hover:underline hover:text-current focus-visible:text-current',
                                                            }) }}
                                                        </li>
                                                    {% endfor %}
                                                </ul>
                                            {% endif %}
                                        </li>
                                    {% default %}
                                        <li class="nav-menu-item text-secondary-500">
                                            {{ menu.mainNavMenuItem.getLink({
                                                class: topMenuItemClass,
                                            }) }}
                                        </li>
                                {% endswitch %}
                            {% endfor %}
                        </ul>
                    </nav>
                </div>

                <div class="w-1/5">
                    {% include "_parts/navigation/search" with {
                        id: 'search-desktop'
                    } only %}
                </div>
            </div>

            <div class="hidden text-sm max-xl:block">
                <div class="flex justify-between w-full px-[1.125rem] py-2 max-lg:border border-secondary-100">
                    <a href="{{ siteUrl }}" aria-label="Terug naar home">
                        <img src="{{ craft.vite.asset("/images/logo.svg", true) }}" alt="{{ 'Logo of the Waarderingskamer' | t }}" width="180" height="30" class="block py-2 pr-2">
                    </a>
                    {% macro menuButtonText(label, icon, positionClass) %}
                        <span class="flex justify-between transition-transform gap-4 flex-row col-start-1 col-end-1 row-start-1 row-end-1 {{ positionClass }}">
                            <span class="inline-flex items-center">{{ label }}</span>
                            {% include '_parts/icon.twig' with {
                                'icon': icon,
                                'class': 'w-6 h-6'
                            } %}
                        </span>
                    {% endmacro %}

                    <button
                        class="grid items-center grid-rows-1 gap-4 overflow-hidden btn btn--primary w-fit group"
                        aria-controls="mobile-nav"
                        aria-expanded="false"
                        aria-label="{{ 'Main menu button' | t }}"
                        @click="open = !open"
                    >
                        {{ _self.menuButtonText('Menu' | t, 'menu', 'translate-y-0 group-aria-expanded:-translate-y-[200%]') }}
                        {{ _self.menuButtonText('Close' | t, 'x', 'translate-y-[200%] group-aria-expanded:translate-y-0') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <nav
        id="mobile-nav"
        class="absolute left-0 w-full overflow-auto h-[calc(100vh_-_4.35rem)] pointer-events-none top-full z-50 data-[expanded=true]:pointer-events-auto"
        aria-label="Menu"
    >
        <div class="accordion-animation-wrapper">
            <div class="min-h-0">
                <div class="bg-white rounded-b-3xl content-wrapper">
                    <ul class="flex flex-col">
                        {% for menu in menus %}
                            {% switch menu.type.handle %}
                                {% case "submenu" %}
                                    {% set submenu = menu.mainNavSubmenu.with(['mainNavSubmenuItemLink']).all() %}
                                    <li class="
                                        relative group font-medium
                                        after:content-[''] after:block after:w-[calc(100%_-_2.25rem)] after:h-px after:mx-auto after:bg-secondary-100
                                    ">
                                        <button class="flex justify-between w-full px-[1.125rem] py-8 aria-expanded:pb-4 no-underline text-secondary-500 focus-visible:text-primary-700 aria-expanded:font-bold aria-expanded:text-primary-700 group" aria-controls="mobile-submenu-{{ menu.id }}" aria-expanded="false">
                                            {{ menu.navLabel }}

                                            {% include '_parts/icon.twig' with {
                                                'icon': 'chevron-omlaag',
                                                'class': 'w-5 h-5 group-aria-expanded:rotate-180 transition-transform'
                                            } %}
                                        </button>

                                        {% if submenu %}
                                            <div id="mobile-submenu-{{ menu.id }}" class="overflow-hidden">
                                                <div class="accordion-animation-wrapper">
                                                    <div class="min-h-0">
                                                        <ul class="pb-4 content-wrapper">
                                                            {% for item in submenu %}
                                                                <li class="font-medium">
                                                                    {{ item.mainNavSubmenuItemLink.getLink({
                                                                        class: 'px-[1.125rem] py-4 block text-secondary-500 no-underline underline-offset-8 hover:underline',
                                                                    }) }}
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </li>
                                {% default %}
                                    <li class="
                                        nav-menu-item font-medium
                                        after:content-[''] after:block after:w-[calc(100%_-_2.25rem)] after:h-px after:mx-auto after:bg-secondary-100
                                    ">
                                        {{ menu.mainNavMenuItem.getLink({
                                            class: 'px-[1.125rem] py-8 block text-secondary-500 no-underline hover:text-primary-700 visited:text-secondary-500 focus-visible:text-primary-700',
                                        }) }}
                                    </li>
                            {% endswitch %}
                        {% endfor %}
                    </ul>

                    <div class="px-[1.125rem] py-8">
                        {% include "_parts/navigation/search" with {
                            id: 'search-mobile'
                        } only %}
                    </div>
                </div>
            </div>
        </div>
    </nav>
</div>
