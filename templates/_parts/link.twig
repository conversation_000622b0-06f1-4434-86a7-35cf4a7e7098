{% set label = linkItem.customText %}
{% set href = linkItem.linkedUrl ? linkItem.linkedUrl : linkItem.linkedId %}

{% if href matches '/^\\d+$/' and linkItem.type == 'entry' and label is empty %}
    {% set entry = craft.entries.id(href).one() %}
    {% set label = entry.title %}
{% endif %}

<a class="{{ class | default('') }}" {{ linkItem.getLinkAttributes() }}>
    {{ label | default(linkItem.url) }}
</a>