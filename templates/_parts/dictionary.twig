{% set words = craft.entries().section('dictionary').all() | sort((a, b) => a.title <=> b.title) %}

{% set dictionary = range('A', 'Z') | map(letter => {
    key: letter,
    words: words | filter(word => (word.title | upper | first) == letter)
}) %}

{% block content %}
    <div class="container pt-8 pb-10 lg:pt-16 container--padding lg:pb-36">
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-sidebar lg:grid-areas-sidebar dictionary__wrapper">
            <div class="relative flex flex-col lg:grid-in-sidebar">
                <div class="flex flex-col gap-4 p-6 lg:sticky top-20 bg-secondary-100 justify-items-center rounded-3xl">
                    <h2 class="h4">{{ 'View by letter' | t }}</h2>
                    <div class="grid grid-cols-[repeat(auto-fit,minmax(40px,1fr))] -mx-4">
                        {% for letter in dictionary %}
                            {% if letter.words | length == 0 %}
                                <div class="p-4 no-underline opacity-30 text-secondary-900">
                                    {{ letter.key }}
                                </div>
                            {% else %}
                                <a href="#letter-{{ letter.key | lower }}" class="p-4 no-underline duration-200 text-secondary-900 hover:text-tertiary-500">
                                    {{ letter.key }}
                                </a>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="flex flex-col gap-20 lg:grid-in-content">
                <ul data-accordion-wrapper class="relative flex flex-col p-0 m-0 bg-white rounded-2xl">
                    {% for letter in dictionary %}
                        <li class="flex flex-col m-0">
                            <h4 id="letter-{{ letter.key | lower }}" class="inline-flex items-center justify-between w-full p-6 no-underline text-secondary-900 scroll-mt-20 max-lg:scroll-mt-44">
                                {{ letter.key }}
                            </h4>
                            {% if letter.words | length > 0 %}
                                {% include "_parts/accordion/set" with {
                                    items: letter.words
                                } %}
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
{% endblock %}
