{% set contactForm = settings.linkButton %}
{% set tel = settings.mainTelephone %}
{% set addresses = settings.mainAddress.all() %}
{% set footerMenu = settings.footerMenu.all() %}
{% set footerEnd = settings.footerEnd.all() %}

<footer class="bg-secondary mt-7 md:mt-10" aria-labelledby="footer-heading">
    <div class="container container--padding">
        <div class="grid border-b py-14 lg:grid-cols-4 border-primary-300">
            <div class="lg:col-span-3">
                <h2 id="footer-heading" class="text-white h3">{{ 'Do you have a question about the WOZ?' | t }}</h2>
                <div class="flex flex-col pt-4 xl:flex-row xl:items-center">
                    <p class="font-bold text-white">{{ 'Feel free to contact us!' | t }}</p>
                    <ul class="flex flex-col pt-6 lg:flex-row xl:pt-0">
                        {% if contactForm is defined and contactForm is not empty %}
                            <li class="pb-4 xl:pb-0 lg:pl-5">
                                <a href="{{ contactForm }}" class="inline-flex items-center pl-4 btn btn--tertiary focus-visible:text-white hover:!text-secondary-700">
                                    {% include '_parts/icon.twig' with {
                                        'icon': 'e-mail',
                                        'class': 'mr-2 w-6 h-6'
                                    } %}
                                    <span>{{ contactForm.customText }}</span>
                                </a>
                            </li>
                        {% endif %}

                        {% if tel is defined and tel is not empty %}
                            <li class="pb-4 xl:pb-0 lg:pl-5">
                                <a href="{{ tel }}" class="inline-flex items-center pl-4 btn btn--tertiary focus-visible:text-white hover:!text-secondary-700" target="_blank">
                                    {% include '_parts/icon.twig' with {
                                        'icon': 'telefoon',
                                        'class': 'mr-2 w-6 h-6'
                                    } %}
                                    <span>{{ tel.customText }}</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>

            {# socials #}
            {% include '_parts/footer/social-media.twig' %}
        </div>

        <div class="grid items-start border-b py-14 border-primary-300 lg:grid-cols-4">
            <nav aria-labelledby="footer-nav" class="lg:col-span-3">
                <h3 id="footer-nav" class="sr-only">{{ 'Footer navigation' | t }}</h3>

                {% if footerMenu is defined and footerMenu is not empty %}
                    <ul class="xl:grid xl:grid-cols-3">
                        {% for submenu in footerMenu %}
                            <li class="py-5 text-white label lg:py-4 xl:py-0 lg:pr-4">
                                {{ submenu.footerMenuTitle }}

                                <ul class="pt-2">
                                    {% for item in submenu.footerMenuItems %}
                                        <li class="p-small">
                                            {{ item.footerMenuItemsRepeaterLink.getLink({
                                                class: 'text-white no-underline py-1 inline-block underline-offset-8 hover:text-white hover:underline focus-visible:text-white focus-visible:underline lg:py-2 lg:pr-2',
                                            }) }}
                                        </li>
                                    {% endfor %}
                                </ul>
                            </li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </nav>
            {% if addresses is defined and addresses is not empty %}
                <div class="pt-5 lg:pt-0">
                    <h3 class="text-white label">{{ 'Contact' | t }}</h3>

                    {% for address in addresses %}
                        <address class="flex flex-row py-3 not-italic text-white lg:flex-col">
                            {% include '_parts/icon.twig' with {
                                'icon': address.mainAddressIcon.label,
                                'class': 'w-6 h-6 max-lg:mt-1'
                            } %}
                            <div class="pl-2 address-text--white lg:pl-0 p-small">
                                {{ address.mainAddressAddress }}
                            </div>
                        </address>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <nav class="py-4" aria-labelledby="footer-breadcrumbs-heading">
            <h3 id="footer-breadcrumbs-heading" class="sr-only">{{ 'Footer end navigation' | t }}</h3>

            <ul class="flex flex-row flex-wrap items-center gap-x-8 gap-y-2">
                <li><p class="text-white breadcrumb">&copy {{ now | date('Y') }} {{ 'Waarderingskamer' | t }}</p></li>

                {% if footerEnd is defined and footerEnd is not empty %}
                    {% for link in footerEnd %}
                        <li><a href="{{ link.footerEndLink }}" class="text-white no-underline breadcrumb underline-offset-8 hover:text-white hover:underline focus-visible:text-white focus-visible:underline">{{ link.footerEndLink.customText }}</a></li>
                    {% endfor %}
                {% endif %}
            </ul>
        </nav>
   </div>
</footer>
