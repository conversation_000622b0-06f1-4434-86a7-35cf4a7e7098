{% set loadingType = "lazy" %}

{% if loading is defined and loading != "lazy" and loading == "eager" %}
    {% set loadingType = "eager" %}
{% endif %}

{% if sizes is not defined %}
    {% set sizes = ['0.25x', '0.5x', '1x', '1.5x'] %}
{% endif %}

{% set resize = {
    mode: mode is defined ? mode : 'crop',
    width: width is defined ? width : 0,
    height: height is defined ? height : 0,
    quality: quality is defined ? quality : 80,
} %}

{% if image %}
    {% if image.extension != 'svg' or format is defined %}
        {% set resize = resize | merge({
            format: format is defined ? format : 'webp'
        }) %}
    {% endif %}

    {% if image.extension != 'svg' %}
        {% set imageUrlsBySizes = image.getUrlsBySize(sizes, resize) | map((url, size) => "#{url} #{size}") | join(', ') %}
        {% set imageUrl = image.getUrl(resize) %}
    {% else %}
        {% set imageUrl = image.url %}
    {% endif %}

    {% set width = width ?? image.getWidth() %}
    {% set height = height ?? image.getHeight() %}

    <img
        {% if class is defined %} class="{{ class }}"{% endif %}
        loading="{{ loadingType }}"
        {% if draggable is defined and draggable == 'false' %}draggable="false"{% endif %}
        {% if attributes is defined %}{{ attributes }}{% endif %}
        {% if imageUrlsBySizes is defined %}srcset="{{ imageUrlsBySizes }}"{% endif %}
        width="{{ width }}"
        height="{{ height }}"
        src="{{ imageUrl }}"
        alt="{{ alt is defined ? alt : image.alt }}"
    />
{% endif %}
