{#
Usage:
{% set video = block.videoSrc | default() %}
{% set subtitles = entry.videoSubtitles.all() ? entry.videoSubtitles.all() : [] %}
{% include "_parts/video.twig" with {
    video: block.videoSrc,
    subtitles: subtitles
} %}
#}

<video width="1280" height="720" controls class="w-full h-full rounded-3xl">
    <source src="{{ video }}">
    {% if subtitle and subtitle.url is defined and subtitle.url is not empty %}
        <track kind="subtitles" src="{{ subtitle.url }}" srclang="nl" label="Nederlands">
    {% endif %}
</video>