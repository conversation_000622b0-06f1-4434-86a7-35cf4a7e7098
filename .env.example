# General Settings
CP_TRIGGER=beheer
PRIMARY_SITE_URL=https://waarderingskamer.nl.internal/
CRAFT_ENVIRONMENT=dev
CRAFT_APP_ID=CraftCMS--7bf05c8d-e9d4-443b-9e95-eee5b735fafc
CRAFT_SECURITY_KEY= # find me in 1password

# Meilisearch
MEILISEARCH_MASTER_KEY= # Find me in 1Password (Meilisearch MasterKey | Waarderingskamer)
MEILISEARCH_HOST=http://meilisearch
MEILISEARCH_PORT=7700
MEILISEARCH_INDEX_PREFIX="lcl"
MEILISEARCH_CLIENT_HOST=https://search.waarderingskamer.nl.internal
MEILISEARCH_CLIENT_KEY= # Create a search key in Meilisearch and paste it here

# Formie
FRIENDLYCAPTCHA_SITE_KEY=
FRIENDLYCAPTCHA_API_KEY=

# Database Configuration
CRAFT_DB_DRIVER=mysql
CRAFT_DB_SERVER=db
CRAFT_DB_PORT=3306
CRAFT_DB_DATABASE=db
CRAFT_DB_USER=db
CRAFT_DB_PASSWORD=db
CRAFT_DB_SCHEMA=
CRAFT_DB_TABLE_PREFIX=

# SMTP
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USE_AUTHENTICATION=false
SMTP_ENCRYPTION_TYPE=none

# WOZ-IT
WOZ_IT_API_URI=https://preview.wem.io/43156/webservices/publieksportaal/
WOZ_IT_USERNAME=is42_preview
WOZ_IT_PASSWORD=ThypV3@

WOZ_BENCHMARK_DASHBOARD_API_URI=op://Operations/uiefruotex4or2ersdfxuwz4d4/WOZ_BENCHMARK_DASHBOARD_API_URI
WOZ_BENCHMARK_BEARER_TOKEN=op://Operations/l63ry5wzg3spqv3licihlrd3y4/WOZ_BENCHMARK_BEARER_TOKEN
