<?php

/**
 * Yii Application Config
 *
 * Edit this file at your own risk!
 *
 * The array returned by this file will get merged with
 * vendor/craftcms/cms/src/config/app.php and app.[web|console].php, when
 * Craft's bootstrap script is defining the configuration for the entire
 * application.
 *
 * You can define custom modules and system components, and even override the
 * built-in system components.
 *
 * If you want to modify the application config for *only* web requests or
 * *only* console requests, create an app.web.php or app.console.php file in
 * your config/ folder, alongside this one.
 */

use craft\helpers\App;
use modelupload\ModelUploadModule;
use modules\documentresolver\DocumentResolver;
use modules\twigextender\TwigExtenderModule;
use organization\OrganizationModule;
use redkiwi\craftbase\Module as CraftBaseModule;
use removemetadata\RemoveMetadataModule;
use search\SearchModule;

return [
    '*' => [
        'id' => App::env('CRAFT_APP_ID') ?: 'CraftCMS',
        'modules' => [
            'redkiwi-base' => CraftBaseModule::class,
            'organization' => OrganizationModule::class,
            'twig-extender' => TwigExtenderModule::class,
            'search' => SearchModule::class,
            'model-upload' => ModelUploadModule::class,
            'remove-metadata' => RemoveMetadataModule::class,
            'document-resolver' => DocumentResolver::class
        ],
        'bootstrap' => ['redkiwi-base', 'organization', 'twig-extender', 'search', 'model-upload', 'remove-metadata', 'document-resolver'],
        'components' => [
            'mailer' => function() {
                $settings = craft\helpers\App::mailSettings();
                $settings->transportType = craft\mail\transportadapters\Smtp::class;

                $settings->transportSettings = [
                    'host' => getenv('SMTP_HOST'),
                    'port' => getenv('SMTP_PORT'),
                    'useAuthentication' => false,
                    'timeout' => 10,
                ];

                $config = craft\helpers\App::mailerConfig($settings);

                return Craft::createObject($config);
            },
        ],
    ],
    'production' => [
        'components' => [
            'mailer' => function() {
                $settings = craft\helpers\App::mailSettings();

                $settings->transportType = craft\mail\transportadapters\Sendmail::class;

                $settings->transportSettings = [
                    'command' => '/usr/sbin/sendmail -bs',
                ];

                $config = craft\helpers\App::mailerConfig($settings);

                return Craft::createObject($config);
            }
        ]
    ],
];
