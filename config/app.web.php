<?php

use craft\helpers\App;

$meilisearchUrl = App::env('MEILISEARCH_CLIENT_HOST');

$cspRules = [
    "default-src 'self';",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://waarderingskamer.containers.piwik.pro;",
    "style-src 'self' 'unsafe-inline';",
    "img-src 'self' data:;",
    "font-src 'self';",
    "connect-src 'self' $meilisearchUrl https://waarderingskamer.piwik.pro https://api.friendlycaptcha.com;",
    "media-src 'self' https://player.vimeo.com https://download-video-ak.vimeocdn.com;",
    "object-src 'none';",
    "frame-src 'self' https://app.powerbi.com/;",
    "frame-ancestors 'self';",
    "base-uri 'self';",
    "form-action 'self';",
    'report-uri /csp-report;',
    'report-to default;',
    'worker-src \'self\' blob:',
];

$isDev = App::env('CRAFT_ENVIRONMENT') === 'dev';

$headerType = $isDev ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';

return [
    'as headersFilter' => [
        'class' => \craft\filters\Headers::class,
        'headers' => [
            $headerType => implode(' ', $cspRules),
        ],
    ],
];
