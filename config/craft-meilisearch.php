<?php

use craft\helpers\App;

$host = App::env('MEILISEARCH_HOST');
$port = App::env('MEILISEARCH_PORT');
$prefix = App::env('MEILISEARCH_INDEX_PREFIX');
$masterKey = App::env('MEILISEARCH_MASTER_KEY');

return [
    'host' => $host,
    'port' => $port,
    'prefix' => $prefix,
    'masterKey' => $masterKey,
    'indexes' => [
        'global' => [
            'primaryKey' => 'uid',
            'sections' => ['page', 'organization', 'documents'],
            'filterableAttributes' => ['categories.title', 'type.name'],
            'sortableAttributes' => ['postDate', 'title'],
            'behavior' => 'sections',
        ],
        'organization' => [
            'primaryKey' => 'uid',
            'sections' => ['organization'],
            'filterableAttributes' => [],
            'sortableAttributes' => []
        ],
        'downloads' => [
            'primaryKey' => 'uid',
            'sections' => ['documents'],
            'filterableAttributes' => ['documents.title'],
            'sortableAttributes' => [],
            'behavior' => 'assets',
        ],
    ],
    'afterAssetTransform' => function (\craft\elements\Asset $asset, array $transformedAsset): array {
        if (strtolower($asset->getVolume()->name) === 'documents' && isset($transformedAsset['publish'])) {
            $site = Craft::$app->getSites()->getSiteById($asset->siteId);
            if ($site !== null) {
                foreach ($transformedAsset['publish'] as $key => $options) {
                    foreach ($options as $option) {
                        if ($option === $site->language) {
                            $transformedAsset['type'] = [
                                'name' => 'Publicatie',
                                'handle' => 'publication'
                            ];
                        }
                    }
                }
            }
        }

        return $transformedAsset;
    }
];
