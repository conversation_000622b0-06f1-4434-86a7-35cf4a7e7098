columnSuffix: null
contentColumnType: string
fieldGroup: 9bd8fddf-145f-4b84-b085-69c7a4ff3a91 # Common
handle: imageResponsive
instructions: '<PERSON><PERSON><PERSON>, mogelijkheid om een andere afbeelding voor mobiel formaat te gebruiken. '
name: 'Mobiele afbeelding'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds: null
  branchLimit: null
  defaultUploadLocationSource: 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  defaultUploadLocationSubpath: null
  localizeRelations: false
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  previewMode: full
  restrictFiles: false
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  restrictedLocationSubpath: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Asset
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\assets\AssetCondition
  selectionLabel: null
  showSiteMenu: false
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources:
    - 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: site
type: craft\fields\Assets
