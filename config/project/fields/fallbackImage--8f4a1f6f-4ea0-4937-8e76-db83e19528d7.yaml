columnSuffix: null
contentColumnType: string
fieldGroup: 3b0775e6-f32b-42b7-8264-793d277c3bcf # Video element
handle: fallbackImage
instructions: 'This is the image that will be shown in case that the user has not accepted cookies.'
name: 'Fallback image'
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds:
    - image
  branchLimit: null
  defaultUploadLocationSource: 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  defaultUploadLocationSubpath: null
  localizeRelations: false
  maintainHierarchy: false
  maxRelations: null
  minRelations: 1
  previewMode: full
  restrictFiles: true
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: null
  restrictedLocationSubpath: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Asset
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\assets\AssetCondition
  selectionLabel: null
  showSiteMenu: false
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources:
    - 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: site
type: craft\fields\Assets
