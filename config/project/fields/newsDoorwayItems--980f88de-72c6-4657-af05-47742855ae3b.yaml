columnSuffix: null
contentColumnType: string
fieldGroup: 5cdb777a-31d7-4a5b-afae-7a7911507b45 # Doorway Elements
handle: newsDoorwayItems
instructions: null
name: 'Nieuws doorway items'
searchable: false
settings:
  blockTypeFields: 0
  changedFieldIndicator: 1881167563
  columns:
    __assoc__:
      -
        - ef546391-64be-4b1c-9c42-a5bfc77de17c # Titel
        -
          __assoc__:
            -
              - width
              - ''
      -
        - 8141125d-0b86-4275-940c-903c002029c0 # Subtitel
        -
          __assoc__:
            -
              - width
              - ''
      -
        - f6b8fad2-9308-4fc8-b068-7b8e5fcf9df5 # Entry
        -
          __assoc__:
            -
              - width
              - ''
  contentTable: '{{%stc_newsdoorwayitems}}'
  fieldLayout: matrix
  maxRows: null
  minRows: null
  propagationKeyFormat: null
  propagationMethod: none
  selectionLabel: 'Voeg doorway toe'
  staticField: null
translationKeyFormat: null
translationMethod: none
type: verbb\supertable\fields\SuperTableField
