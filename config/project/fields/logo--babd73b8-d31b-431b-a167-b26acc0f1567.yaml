columnSuffix: null
contentColumnType: string
fieldGroup: f3b150be-b976-45b1-bfcb-1ac38811f0bf # API
handle: logo
instructions: null
name: Logo
searchable: false
settings:
  allowSelfRelations: false
  allowSubfolders: false
  allowUploads: true
  allowedKinds:
    - image
  branchLimit: null
  defaultUploadLocationSource: 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  defaultUploadLocationSubpath: null
  localizeRelations: false
  maintainHierarchy: false
  maxRelations: 1
  minRelations: null
  previewMode: full
  restrictFiles: true
  restrictLocation: false
  restrictedDefaultUploadSubpath: null
  restrictedLocationSource: 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  restrictedLocationSubpath: null
  selectionCondition:
    __assoc__:
      -
        - elementType
        - craft\elements\Asset
      -
        - fieldContext
        - global
      -
        - class
        - craft\elements\conditions\assets\AssetCondition
  selectionLabel: null
  showSiteMenu: false
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  sources:
    - 'volume:d8ce6a61-181d-42f2-9fa2-fea9e2aa7875' # Images
  targetSiteId: null
  validateRelatedElements: false
  viewMode: list
translationKeyFormat: null
translationMethod: site
type: craft\fields\Assets
