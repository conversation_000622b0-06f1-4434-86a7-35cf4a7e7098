childBlocks: null
conditions: null
description: ''
enabled: true
field: 55016e42-f626-4a5b-9b0a-facff7e2ce1c # Content blokken
fieldLayouts:
  804ef289-35bf-4884-8539-ceb775b4dd2a:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 335ce7be-8059-41bd-9b8d-eaf6f500e8f5 # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c06f3946-56db-4c00-abce-e453cf122efe
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: aac7ec81-dc8b-4390-a89e-bc6a6652342b # Link button
            instructions: null
            label: Button
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: df50c0bf-c9c8-46c7-94dc-1c7225432c44
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 980f88de-72c6-4657-af05-47742855ae3b # Nieuws doorway items
            instructions: null
            label: Items
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d3138d71-815b-4718-a24a-5968dd53e8cd
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 324a8e85-f303-4852-9b58-04b09c90a28b
        userCondition: null
      -
        elementCondition:
          class: benf\neo\elements\conditions\BlockCondition
          conditionRules:
            -
              class: benf\neo\elements\conditions\OwnerEntryTypeConditionRule
              operator: in
              uid: d7081d39-6761-473f-9134-4815880216fb
              values:
                - 7b9de500-ff5b-4a4f-94d8-660cc7aa9412 # Thema
                - 87d79b5e-ae56-4123-8c15-d33fac73e9f3 # Nieuws
          elementType: benf\neo\elements\Block
          fieldContext: global
        elements:
          -
            elementCondition: null
            fieldUid: 4c9bb21d-d9ce-4659-adc2-52b23f091262 # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c8a34fb3-298b-4759-9ef3-54d41c6a1ff4
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 3e5f0775-9520-4ee8-b586-5c452bcad6f3 # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: cab45a19-a82d-406b-9abe-3c77cb2a6ee3
            userCondition: null
            warning: null
            width: 50
        name: 'Quick link'
        uid: d1dc3bb6-ae78-4355-ba55-200ad278dce1
        userCondition: null
group: e4232874-0b5c-4737-a87f-b500a66ae8a7 # Doorways
groupChildBlockTypes: true
handle: newsDoorway
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Nieuws doorway'
topLevel: true
