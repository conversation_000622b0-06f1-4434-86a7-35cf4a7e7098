childBlocks: null
conditions: null
description: ''
enabled: true
field: 55016e42-f626-4a5b-9b0a-facff7e2ce1c # Content blokken
fieldLayouts:
  1750f109-dcdc-4357-88e2-505d6b048031:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 335ce7be-8059-41bd-9b8d-eaf6f500e8f5 # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 718f4208-fcc0-4600-8d80-58fe97c53798
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: aac7ec81-dc8b-4390-a89e-bc6a6652342b # Link button
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a207bf3d-cc97-49c1-a8ae-f7f8b828a471
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 931ee74b-7ae2-4720-894b-b2f5f546e957 # Basis doorway items
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a4eb92b2-4202-419f-ace6-4d46d8ce5f9e
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 3236d89b-2a58-4b0d-b264-4a267209a1d5
        userCondition: null
      -
        elementCondition:
          class: benf\neo\elements\conditions\BlockCondition
          conditionRules:
            -
              class: benf\neo\elements\conditions\OwnerEntryTypeConditionRule
              operator: in
              uid: 06f87487-eeba-47b0-ad80-f889e2230594
              values:
                - 7b9de500-ff5b-4a4f-94d8-660cc7aa9412 # Thema
                - 87d79b5e-ae56-4123-8c15-d33fac73e9f3 # Nieuws
          elementType: benf\neo\elements\Block
          fieldContext: global
        elements:
          -
            elementCondition: null
            fieldUid: 4c9bb21d-d9ce-4659-adc2-52b23f091262 # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e81ea4b4-f2d7-4f4c-9132-f9259c2f85ab
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 3e5f0775-9520-4ee8-b586-5c452bcad6f3 # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ebf3b3a1-775b-42c5-91ea-02e5288360b0
            userCondition: null
            warning: null
            width: 50
        name: 'Quick link'
        uid: 9a08bd02-59ff-40bc-a0e1-068bd6255861
        userCondition: null
group: e4232874-0b5c-4737-a87f-b500a66ae8a7 # Doorways
groupChildBlockTypes: true
handle: baseDoorway
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Basis doorway'
topLevel: true
