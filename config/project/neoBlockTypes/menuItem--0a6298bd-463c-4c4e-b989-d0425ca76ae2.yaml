childBlocks: null
conditions: null
description: ''
enabled: true
field: a3f6ab19-e03b-4363-a4e1-4587da505a1c # Footer menu
fieldLayouts:
  7e55ea7f-b396-47d2-b1d4-a6aab4209fb8:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 8145544b-a369-4ef9-b030-c3228f0398e2 # Titel
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 723e813b-4981-4ca2-9f61-8edf5d22876a
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 20ca38d2-6e08-4cd3-92c2-b3b55966a626 # Footer menu items
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 96ce47b7-872a-4478-a8df-122e0ec2cbb9
            userCondition: null
            warning: null
            width: 100
        name: 'Menu item'
        uid: 7ea6ad1f-a1bd-4a96-ba19-c49257cbca9b
        userCondition: null
group: null
groupChildBlockTypes: true
handle: menuItem
icon: null
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: 'Menu item'
sortOrder: 1
topLevel: true
