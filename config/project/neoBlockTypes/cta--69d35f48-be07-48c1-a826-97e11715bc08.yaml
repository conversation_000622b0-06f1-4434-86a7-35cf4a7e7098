childBlocks: null
conditions: null
description: ''
enabled: true
field: 55016e42-f626-4a5b-9b0a-facff7e2ce1c # Content blokken
fieldLayouts:
  533dbe22-7eb8-4f01-9986-808f6cdbe4bc:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 335ce7be-8059-41bd-9b8d-eaf6f500e8f5 # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 92b10b1e-1f08-4a6f-8800-50cf983e2f48
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 4e42b5d6-9998-4da2-904c-e6e8d0407712 # Layout
                  operator: ni
                  uid: e60ebe01-df22-4c15-8a20-3e58b4e11418
                  values:
                    - small
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: c63b3b41-9fe4-48f3-a98a-b63c5161d377 # Afbeelding
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: f9bc2247-4760-4fc1-b236-a43174da693b
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition:
              class: benf\neo\elements\conditions\BlockCondition
              conditionRules:
                -
                  class: craft\fields\conditions\OptionsFieldConditionRule
                  fieldUid: 4e42b5d6-9998-4da2-904c-e6e8d0407712 # Layout
                  operator: ni
                  uid: 56d6bf66-a411-4f56-89c8-c00a1eb90569
                  values:
                    - small
              elementType: benf\neo\elements\Block
              fieldContext: global
            fieldUid: ebe0e135-c4a5-4d48-8293-d099092a2a0e # Tekst
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b76a257d-9354-47a6-8a55-6d98e77e3f88
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 0c6f9074-9bfa-45ea-8b20-364ba76de315 # Button
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8ecd9b46-dd59-4b6c-8adf-08905503744f
            userCondition: null
            warning: null
            width: 50
        name: Content
        uid: 7ccb2582-6bee-4cd0-8a80-b0e735371e1d
        userCondition: null
      -
        elementCondition:
          class: benf\neo\elements\conditions\BlockCondition
          conditionRules:
            -
              class: benf\neo\elements\conditions\OwnerEntryTypeConditionRule
              operator: ni
              uid: 692e5f2b-76b8-4a29-ac86-f3ba59a893c5
              values:
                - 7b9de500-ff5b-4a4f-94d8-660cc7aa9412 # Thema
          elementType: benf\neo\elements\Block
          fieldContext: global
        elements:
          -
            elementCondition: null
            fieldUid: 4e42b5d6-9998-4da2-904c-e6e8d0407712 # Layout
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b9165005-d744-490d-812b-25e83c785734
            userCondition: null
            warning: null
            width: 100
        name: Layout
        uid: 64cf89dd-2927-44dd-8777-792f91ee62dd
        userCondition: null
      -
        elementCondition:
          class: benf\neo\elements\conditions\BlockCondition
          conditionRules:
            -
              class: benf\neo\elements\conditions\OwnerEntryTypeConditionRule
              operator: in
              uid: 9135e245-5e79-4289-8db2-341aafe2cc2b
              values:
                - 7b9de500-ff5b-4a4f-94d8-660cc7aa9412 # Thema
                - 87d79b5e-ae56-4123-8c15-d33fac73e9f3 # Nieuws
          elementType: benf\neo\elements\Block
          fieldContext: global
        elements:
          -
            elementCondition: null
            fieldUid: 4c9bb21d-d9ce-4659-adc2-52b23f091262 # Icoon
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 56db5ff2-19fb-4f9f-98b4-30f381261c8e
            userCondition: null
            warning: null
            width: 50
          -
            elementCondition: null
            fieldUid: 3e5f0775-9520-4ee8-b586-5c452bcad6f3 # Titel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 238271f1-e00c-4670-b461-5348f80be015
            userCondition: null
            warning: null
            width: 50
        name: 'Quick link'
        uid: 7beee56c-1eab-469f-ae1c-e972cd1d804f
        userCondition: null
group: null
groupChildBlockTypes: true
handle: cta
icon: null
iconFilename: ''
ignorePermissions: true
maxBlocks: 0
maxChildBlocks: 0
maxSiblingBlocks: 0
minBlocks: 0
minChildBlocks: 0
minSiblingBlocks: 0
name: CTA
topLevel: true
