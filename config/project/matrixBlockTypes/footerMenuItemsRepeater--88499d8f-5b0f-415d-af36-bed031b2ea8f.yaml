field: 20ca38d2-6e08-4cd3-92c2-b3b55966a626 # Footer menu items
fieldLayouts:
  8ede461f-f4a4-498a-bbee-2213e27bf200:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 85032e7c-b19c-4058-b6e5-0f73b8b993ad # Menu item link
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 8e8a4ea9-01d1-4e37-8e80-45c859c7d725
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: b7204be4-9af8-44d1-8a17-70c4b69bd95a
        userCondition: null
fields:
  85032e7c-b19c-4058-b6e5-0f73b8b993ad: # Menu item link
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: footerMenuItemsRepeaterLink
    instructions: null
    name: 'Menu item link'
    searchable: false
    settings:
      allowCustomText: true
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: true
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: false
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: false
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: language
    type: lenz\linkfield\fields\LinkField
handle: footerMenuItemsRepeater
name: 'Menu item'
sortOrder: 1
