changedFieldIndicator: 720404081
field: fdd314bd-118e-41f6-a4a9-e605ebcc63ea # Thema doorway items
fieldLayouts:
  e8ee7264-0f40-4453-806f-7a9dd96d08ff:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: 6ca4c1ad-0679-4b02-810f-e29c0914a975 # Icoon
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5ec32070-f727-43ea-9851-4b32b85f7baf
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 67e12941-10a1-4eb8-a421-8f4ee0198626 # Titel
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 186a12b9-348c-453a-84a5-9a7aae9acd4c
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 73f4679f-a73a-4dd2-a27b-5a02aff6dfef # Subtitel
            instructions: null
            label: null
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 51c3382a-f15a-482e-bd3e-cceb026c4770
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: d20db1d5-a0dd-42f7-b22d-01fb82dca73a # Entry
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a882c831-f826-4536-b980-2c5a81d0fea4
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: aab7546f-f7f5-494d-8ec3-6cdc0b8cd1f6
        userCondition: null
fields:
  6ca4c1ad-0679-4b02-810f-e29c0914a975: # Icoon
    columnSuffix: jczbqxxw
    contentColumnType: string
    fieldGroup: null
    handle: icon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: false
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
  67e12941-10a1-4eb8-a421-8f4ee0198626: # Titel
    columnSuffix: ixdvglee
    contentColumnType: text
    fieldGroup: null
    handle: heading
    instructions: null
    name: Titel
    searchable: false
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\PlainText
  73f4679f-a73a-4dd2-a27b-5a02aff6dfef: # Subtitel
    columnSuffix: shfvqyhq
    contentColumnType: text
    fieldGroup: null
    handle: subtitle
    instructions: null
    name: Subtitel
    searchable: false
    settings:
      byteLimit: null
      charLimit: null
      code: false
      columnType: null
      initialRows: 4
      multiline: false
      placeholder: null
      uiMode: normal
    translationKeyFormat: null
    translationMethod: site
    type: craft\fields\PlainText
  d20db1d5-a0dd-42f7-b22d-01fb82dca73a: # Entry
    columnSuffix: null
    contentColumnType: string
    fieldGroup: null
    handle: entry
    instructions: null
    name: Entry
    searchable: false
    settings:
      allowCustomText: false
      allowTarget: true
      autoNoReferrer: true
      customTextMaxLength: 0
      customTextRequired: false
      defaultLinkName: entry
      defaultText: ''
      enableAllLinkTypes: false
      enableAriaLabel: false
      enableElementCache: false
      enableTitle: false
      typeSettings:
        asset:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        category:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
        custom:
          allowAliases: false
          disableValidation: false
          enabled: false
        email:
          allowAliases: false
          disableValidation: false
          enabled: false
        entry:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: true
          sources: '*'
        site:
          enabled: false
          sites: '*'
        tel:
          allowAliases: false
          disableValidation: false
          enabled: false
        url:
          allowAliases: false
          disableValidation: false
          enabled: true
        user:
          allowCrossSiteLink: false
          allowCustomQuery: false
          enabled: false
          sources: '*'
    translationKeyFormat: null
    translationMethod: none
    type: lenz\linkfield\fields\LinkField
