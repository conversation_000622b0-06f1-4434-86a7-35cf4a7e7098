changedFieldIndicator: 789867255
field: 3dae8b4b-9699-4091-8b87-3d507c669c03 # Adres
fieldLayouts:
  e14bd804-37ba-4b88-8aab-df56e9482d09:
    tabs:
      -
        elementCondition: null
        elements:
          -
            elementCondition: null
            fieldUid: db9bfa68-8708-4d1a-a698-c73c7c560032 # Icoon
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: a81fc303-df62-4a4f-9828-9b642d0eb601
            userCondition: null
            warning: null
            width: 100
          -
            elementCondition: null
            fieldUid: 0ec8eb75-cbdf-4ba2-9cf9-852d09602c64 # Adres
            instructions: null
            label: null
            required: true
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e8cb7f0a-abdb-419e-bc5d-90ea95b7616b
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 5d198b16-55fc-4766-ac03-d68b4f64aa5f
        userCondition: null
fields:
  0ec8eb75-cbdf-4ba2-9cf9-852d09602c64: # Adres
    columnSuffix: kpooosbo
    contentColumnType: text
    fieldGroup: null
    handle: mainAddressAddress
    instructions: 'Straat en nummer'
    name: Adres
    searchable: false
    settings:
      availableTransforms: '*'
      availableVolumes: '*'
      columnType: text
      configSelectionMode: choose
      defaultTransform: ''
      manualConfig: ''
      purifierConfig: null
      purifyHtml: true
      redactorConfig: null
      removeEmptyTags: false
      removeInlineStyles: false
      removeNbsp: false
      showHtmlButtonForNonAdmins: false
      showUnpermittedFiles: false
      showUnpermittedVolumes: false
      uiMode: enlarged
    translationKeyFormat: null
    translationMethod: none
    type: craft\redactor\Field
  db9bfa68-8708-4d1a-a698-c73c7c560032: # Icoon
    columnSuffix: aevprfsi
    contentColumnType: string
    fieldGroup: null
    handle: mainAddressIcon
    instructions: null
    name: Icoon
    searchable: false
    settings:
      columnType: text
      iconSets: '*'
      renderId: null
      showLabels: false
    translationKeyFormat: null
    translationMethod: none
    type: verbb\iconpicker\fields\IconPickerField
