module.exports = {
    plugins: [
        {
            name: 'preset-default',
            params: {
                overrides: {
                    removeViewBox: false,
                    convertColors: {
                        currentColor: true,
                    },
                },
            },
        },
        {
            name: 'removeOffCanvasPaths',
        },
        {
            name: 'removeScriptElement',
        },
        {
            name: 'removeStyleElement',
        },
        {
            name: 'removeDimensions',
        },
        {
            name: 'addClassesToSVGElement',
            params: {
                className: 'inline fill-current',
            },
        },
        {
            name: 'addAttributesToSVGElement',
            params: {
                attributes: [
                    {
                        'aria-hidden': true,
                    },
                    {
                        role: 'img',
                    },
                    {
                        focusable: false,
                    },
                ],
            },
        },
    ],
};
