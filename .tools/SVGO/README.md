# SVGO

SVG Optimizer is a Node.js-based tool for optimizing SVG vector graphics files.
[https://github.com/svg/svgo](https://github.com/svg/svgo)

## Installation

### NPM

`npm -g install svgo`

### Yarn

`yarn global add svgo`

## Usage

1. Install SVGO
2. Move the SVG's that should be optimized in the `.tools/SVGO` directory
3. Run one of the build commands

## Build commands

### Default optimization

This build will use `svgo.config.js` and output the new SVG files in `src/public/images`.
`svgo -f src -o ./../../src/public/images`

### Icon optimization

This build will use `icon.config.js` and output the new SVG files in `src/public/images/icon`.
`svgo -f src -o ./../../src/public/images/icon --config ./icon.config.js`
