<?php

namespace App\TwigCs;

use FriendsOfTwig\Twigcs\RegEngine\RulesetBuilder;
use FriendsOfTwig\Twigcs\RegEngine\RulesetConfigurator;
use FriendsOfTwig\Twigcs\Rule;
use FriendsOfTwig\Twigcs\Ruleset\Official;
use FriendsOfTwig\Twigcs\Ruleset\RulesetInterface;
use FriendsOfTwig\Twigcs\Validator\Violation;
use App\TwigCs\Rules\ForbiddenTags;

class TwigCsRuleset extends Official implements RulesetInterface
{

    public function getRules()
    {
        $configurator = new RulesetConfigurator();

        // One space left and right of pipe
        $configurator->setPropertySpacingPattern('expr.expr | filter');

        $builder = new RulesetBuilder($configurator);

        return [
            new Rule\RegEngineRule(Violation::SEVERITY_ERROR, $builder->build()),
            new Rule\TrailingSpace(Violation::SEVERITY_ERROR),
            new Rule\UnusedMacro(Violation::SEVERITY_WARNING),
            new Rule\ForbiddenFunctions(Violation::SEVERITY_ERROR, ['dump']),
            new ForbiddenTags(Violation::SEVERITY_ERROR, ['dump']),
        ];
    }
}
