<?php

namespace App\TwigCs\Rules;

use FriendsOfTwig\Twigcs\Lexer;
use FriendsOfTwig\Twigcs\Rule\AbstractRule;
use FriendsOfTwig\Twigcs\Rule\RuleInterface;
use FriendsOfTwig\Twigcs\TwigPort\Token;
use FriendsOfTwig\Twigcs\TwigPort\TokenStream;

class ForbiddenTags extends AbstractRule implements RuleInterface
{
    private $tags;

    public function __construct($severity, $tags = [])
    {
        parent::__construct($severity);

        $this->tags = $tags;
    }

    /**
     * @param array $tags
     *
     * @return $this
     */
    public function setTags($tags)
    {
        $this->tags = $tags;

        return $this;
    }

    /**
     * @param string $tag
     *
     * @return $this
     */
    public function addTag($tag)
    {
        if (!in_array($tag, $this->tags, true)) {
            $this->tags[] = $tag;
        }

        return $this;
    }

    public function check(TokenStream $tokens)
    {
        if (empty($this->tags)) {
            return [];
        }

        $violations = [];

        while (!$tokens->isEOF()) {
            $token = $tokens->getCurrent();

            // The current token is a tag name
            if ($this->isTagName($token, $tokens)) {
                // The tag is in the list of forbidden tags
                if (in_array($token->getValue(), $this->tags)) {
                    $violations[] = $this->createViolation(
                        $tokens->getSourceContext()->getPath(),
                        $token->getLine(),
                        $token->getColumn(),
                        sprintf('The tag "%s" is forbidden.', $token->getValue())
                    );
                }
            }

            $tokens->next();
        }

        return $violations;
    }

    private function isTagName(Token $token, TokenStream $tokens)
    {
        return Token::NAME_TYPE === $token->getType() &&
            // The previous token is the opening block token
            Token::BLOCK_START_TYPE === $tokens->look(-2)->getType();
    }

}
