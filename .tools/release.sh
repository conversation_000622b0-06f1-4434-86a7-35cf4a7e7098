#!/bin/sh
set -e

export CRAFT_CONSOLE="${DEPLOY_PATH}/releases/${RELEASE_NAME}/craft"

echo "Make Craft console executable"
chmod +x ${CRAFT_CONSOLE}

echo "Create shared directories if not exists"
if [ ! -d "${DEPLOY_PATH}/shared/storage" ]; then
    cd ${DEPLOY_PATH} && mkdir -p ./shared/storage && mkdir -p ./shared/uploads
fi
if [ ! -f "${DEPLOY_PATH}/shared/.env" ]; then
    cd ${DEPLOY_PATH}/shared/ && touch .env
fi

echo "Symlink shared files & directories into release"
cd ${DEPLOY_PATH}/releases/${RELEASE_NAME} && rm -r storage && ln -sfn ../../shared/storage storage
cd ${DEPLOY_PATH}/releases/${RELEASE_NAME} && ln -sfn ../../shared/.env .env
cd ${DEPLOY_PATH}/releases/${RELEASE_NAME}/web && ln -sfn ../../../shared/uploads uploads

if [ -d "${DEPLOY_PATH}/releases/previous" ]; then
    echo "Unlinking previous release"
    unlink ${DEPLOY_PATH}/releases/previous
fi

if [ -d "${DEPLOY_PATH}/releases/current" ]; then
    echo "Making current release previous"
    cd ${DEPLOY_PATH}/releases && ln -sfn $(readlink -f "current") previous
fi

echo "Symlink new release as current"
cd ${DEPLOY_PATH}/releases && ln -sfn ${RELEASE_NAME} current

echo "Run migrations & config update"
php ${CRAFT_CONSOLE} migrate/all --interactive=0
php ${CRAFT_CONSOLE} project-config/apply --interactive=0

echo "Clearing cache"
php ${CRAFT_CONSOLE} cache/flush-all --interactive=0

echo "Clearing OpCache"
sudo service php8.2-fpm reload

echo "Deleting previous releases"
rm -rf `ls -td ${DEPLOY_PATH}/releases/* | grep -v 'current\|previous' | tail -n +3`
