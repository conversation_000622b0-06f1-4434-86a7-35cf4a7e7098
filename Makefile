.PHONY: build dev composer apply craft pull up env

OP := $(shell command -v op 2> /dev/null)

build: up
	ddev exec npm run build
dev:
	ddev launch
	ddev exec npm run serve
composer: up
	ddev composer \
		$(filter-out $@,$(MAKECMDGOALS))
craft: up
	ddev craft \
		$(filter-out $@,$(MAKECMDGOALS))
apply: up
	ddev craft project-config/apply
	ddev craft clear-caches/all
pull: up
	ddev sync \
		$(filter-out $@,$(MAKECMDGOALS))
	ddev craft users/set-password admin --password redkiwi123
	ddev craft project-config/apply
	ddev craft clear-caches/all
env:
ifndef OP
	$(error "1password cli is not available please install using https://developer.1password.com/docs/cli/get-started")
else
	op inject -i .env.example -o .env
endif
up:
	if [ ! "$$(ddev describe | grep OK)" ]; then \
		ddev auth ssh; \
		ddev start; \
		ddev sync; \
		ddev composer install; \
		ddev exec npm install; \
		ddev exec npm run build; \
	fi
%:
	@:
# ref: https://stackoverflow.com/questions/6273608/how-to-pass-argument-to-makefile-from-command-line
