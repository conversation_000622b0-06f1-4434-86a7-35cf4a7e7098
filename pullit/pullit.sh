#/bin/bash
#title              : pullit.sh
#description        : This script will download the database and import it locally.
#author             : <PERSON><PERSON> <<EMAIL>>
#date               : 18-03-2020
#version            : 0.5.1
#usage              : bash pullit.sh
#==============================================================================
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

function green() {
  printf "${GREEN}$*${NC}\n"
}

function red() {
  printf "${RED}$*${NC}\n"
}

if ! [ -x "$(command -v jq)" ]; then
  red "Error: jq is not installed."
  green "Install this using: \`brew install jq\`" >&2
  exit 1
fi
/usr/bin/clear

# Handle passed flags
export PROJECT_FILE=production.project.json

while true; do
  case "$1" in
    --test )
      export PROJECT_FILE=test.project.json
      shift
      ;;
    --accept )
      export PROJECT_FILE=accept.project.json
      shift
      ;;
    --production )
      export PROJECT_FILE=production.project.json
      shift
      ;;
    --)
      break
      ;;
    -?*)
      printf 'WARN: Unknown option in pullit command (ignored): %s\n' "$1" >&2
      break
      ;;
    *)
      break
      ;;
  esac
done

# ------------------------------------------------------------------------------
# SET VARIABLES
# ------------------------------------------------------------------------------
export EXTERNAL_MYSQL_USER=$(jq -r .externalDatabaseUser $PROJECT_FILE)
export LOCAL_MYSQL_DBNAME=$(jq -r .localDatabaseName $PROJECT_FILE)
export REMOTE_USERNAME=$(jq -r .remoteUsername $PROJECT_FILE)
export REMOTE_HOST_IP=$(jq -r .remoteHostIp $PROJECT_FILE)
export REMOTE_PATH=$(jq -r .remotePath $PROJECT_FILE)
export TOWER_USERNAME=$(jq -r .towerUsername ~/.pullit.json)
export TOWER=$<EMAIL>
# Retrieve password
export DB_PASSWORD=$(ssh -o StrictHostKeyChecking=no -o LogLevel=QUIET -tJ $TOWER $TOWER_USERNAME@$REMOTE_HOST_IP "sudo -u $REMOTE_USERNAME -i cat /etc/my.$EXTERNAL_MYSQL_USER.pass")
# ------------------------------------------------------------------------------

green "Connecting over SSH with user $TOWER_USERNAME"
echo "------------------------------------------------"
green "Downloading database dump for user $EXTERNAL_MYSQL_USER"
ssh -o StrictHostKeyChecking=no -o LogLevel=QUIET -C -tJ $TOWER $TOWER_USERNAME@$REMOTE_HOST_IP "export MYSQL_PWD=$DB_PASSWORD; mysqldump -u$EXTERNAL_MYSQL_USER $REMOTE_USERNAME" --no-tablespaces > db_dump.sql

echo "------------------------------------------------"

green "Importing database dump into DDEV database '$LOCAL_MYSQL_DBNAME'..."
ddev import-db --file=./db_dump.sql

echo "Import done, removing dumpfile."
rm db_dump.sql

echo "------------------------------------------------"

green "Importing files from project"
for key in $(jq -r .paths[] $PROJECT_FILE); do
  rsync --exclude="*."{webp,zip,docx,pdf,pptx,docx,xls,xlsx} -azvL -e "ssh -o StrictHostKeyChecking=no -o LogLevel=QUIET -J $TOWER" $TOWER_USERNAME@$REMOTE_HOST_IP:$REMOTE_PATH$key ../$key
done
echo "------------------------------------------------"
