# Waarderingskamer

## Installation

1. Clone this repo to your local machine.
2. In the project root run `make up` to start or run `make dev`, if you have previously installed this project, to run the dev server and compiler.

### Make commands

| Commands               | Description                                                            |
| ---------------------- | ---------------------------------------------------------------------- |
| make up                | Starts ddev and installs all that is needed                            |
| make dev               | Start vite dev server                                                  |
| make env               | Injects all env variables from 1Password                               |
| make build             | Execute the frontend build                                             |
| make composer $command | Used to execute composer commands inside ddev `make composer update`   |
| make craft $command    | Used to execute composer commands inside ddev `make craft migrate/all` |
| make pull              | Sync the environment using Pullit                                      |
| make apply             | Execute craft project-config/apply                                     |

## Useful links

| Link title                                                                                                   | Description                       |
| ------------------------------------------------------------------------------------------------------------ | --------------------------------- |
| [Documentation](https://craftcms.com/docs/4.x/)                                                              | Craft CMS 4.x Documentation       |
| [Sprig Documentation](https://putyourlightson.com/sprig)                                                     | Sprig Documentation and Recipes   |
| [WOZ-IT API](https://gitlab.redkiwi.nl/customers/waarderingskamer/waarderingskamer/-/wikis/Gemeente-details) | WOZ-IT API data fetching sequence |

## Environments

| Environment | URL                                             | Branch   |
| ----------- | ----------------------------------------------- | -------- |
| Test        | https://test.waarderingskamer.nl.kiwicloud.nl   | `test`   |
| Accept      | https://accept.waarderingskamer.nl.kiwicloud.nl | `accept` |
| Production  | https://www.waarderingskamer.nl                 | `main`   |

## Commands

| Name          | Command                        |
| ------------- | ------------------------------ |
| Gemeente sync | `craft organization/sync/sync` |
| CSV Model sync| `craft model-upload/sync/sync` |

## Configure Meilisearch

1. Copy the Meilisearch masterkey into the env file (can be found in 1Password)
2. Generate a client key. Execute the following command (Replace {MASTER_KEY} with the Meilisearch master key):

```bash
curl \
  -X POST 'https://waarderingskamer.nl.internal:7700/keys' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer {MASTER_KEY}' \
  --data-binary '{
    "description": "Create local client key",
    "actions": ["*"],
    "indexes": ["*"],
    "expiresAt": null
  }'
```

3. Copy the generated key under in the response header to MEILISEARCH_CLIENT_KEY in the env file.
