{"version": "2.0.0", "tasks": [{"label": "<PERSON>wig<PERSON>", "type": "shell", "command": "ddev exec composer run-script lint", "group": "build"}, {"label": "Run PHPStan", "type": "shell", "command": "ddev exec php -d memory_limit=-1 vendor/bin/phpstan analyse modules ", "group": "build"}, {"label": "DDEV: Enable Xdebug", "type": "shell", "command": "ddev xdebug on"}, {"label": "DDEV: Disable Xdebug", "type": "shell", "command": "ddev xdebug off"}]}