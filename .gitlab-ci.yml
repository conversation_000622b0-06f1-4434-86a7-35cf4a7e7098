include:
  - project: 'platforms/craftcms/tools/gitlab-ci'
    file: '.gitlab-ci-template.yml'

'Deploy test':
  extends: .deploy-source
  environment:
    name: test
    url: https://test.waarderingskamer.nl.kiwicloud.nl/
  variables:
    REMOTE_USER: "test"
    REMOTE_HOST: "vps0504.rk.prolocation.net"
  only:
    - master
    - tags

'Deploy accept':
  extends: .deploy-source
  environment:
    name: accept
    url: https://accept.waarderingskamer.nl.kiwicloud.nl/
  variables:
    REMOTE_USER: "accept"
    REMOTE_HOST: "vps0504.rk.prolocation.net"
  only:
    - tags

'Deploy production':
  extends: .deploy-source
  environment:
    name: production
    url: https://www.waarderingskamer.nl/
  variables:
    REMOTE_USER: "production"
    REMOTE_HOST: "vps0511.rk.prolocation.net"
  only:
    - tags
  when: manual
