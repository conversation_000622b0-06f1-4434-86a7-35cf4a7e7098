const defaultTheme = require('tailwindcss/defaultTheme');

/** @type {import('tailwindcss').Config} */
module.exports = {
    corePlugins: {
        container: false,
    },
    plugins: [
        require('@tailwindcss/aspect-ratio'),
        require('@tailwindcss/container-queries'),
        require('@tailwindcss/typography'),
        require('@savvywombat/tailwindcss-grid-areas'),
    ],
    content: [
        './templates/**/*.twig',
        './src/js/**/*.ts',
        './src/public/**/*.svg',
    ],
    theme: {
        fontFamily: {
            sans: ['Montserrat', 'sans-serif'],
        },
        extend: {
            gridTemplateAreas: {
                sidebar: ['sidebar content'],
                wide: ['title void info', 'links void info'],
                slim: ['title', 'info', 'links'],
            },
            gridTemplateColumns: {
                wide: '50% 1fr 30%',
                sidebar: '25% 75%',
                'two-column-table': '1fr auto',
            },
            gridTemplateRows: {
                wide: '1fr \
                        1fr',
            },

            colors: {
                primary: {
                    // teal
                    DEFAULT: '#5FB0B9',
                    100: '#E6F5F6',
                    300: '#A9DCE1',
                    500: '#5FB0B9',
                    700: '#2B7A83',
                    900: '#0A3D43',
                },
                secondary: {
                    // blue
                    DEFAULT: '#11355E',
                    100: '#E8EDF3',
                    300: '#577BA3',
                    400: '#617095',
                    500: '#11355E',
                    700: '#022449',
                    900: '#001731',
                },
                grey: {
                    DEFAULT: '#C9C9C9',
                    300: '#F7F7F7',
                    500: '#C9C9C9',
                    700: '#828282',
                    900: '#2F2F2F',
                },
                error: '#DC1F1F',
            },
            transitionDuration: {
                '400': '400ms',
            },
            minHeight: {
                screenh: '100vh',
            },
            zIndex: (() => {
                const zIndexValues = {};
                for (let i = 1; i <= 9; i++) {
                    zIndexValues[i] = `${i}`;
                }
                return zIndexValues;
            })(),
        },
    },
};
